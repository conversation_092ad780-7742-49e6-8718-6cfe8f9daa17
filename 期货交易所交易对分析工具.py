#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货交易所交易对分析工具
支持币安、OKX、Bybit、Bitget四大交易所
作者: Assistant
版本: 1.0
"""

import requests
import json
from typing import Dict, Set, List, Optional, Tuple
import pandas as pd
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
import time
import logging
from datetime import datetime
import argparse
import sys
import re
import asyncio
import websockets
import threading
from concurrent.futures import ThreadPoolExecutor
import gzip

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FuturesAnalyzer:
    """期货交易对分析器"""
    
    def __init__(self, use_cache: bool = True, cache_duration: int = 3600):
        """
        初始化分析器
        
        Args:
            use_cache: 是否使用缓存
            cache_duration: 缓存持续时间(秒)
        """
        self.exchanges = {
            'binance': {
                'name': 'Binance',
                'func': self.get_binance_futures,
                'url': 'https://fapi.binance.com/fapi/v1/exchangeInfo',
                'ws_url': 'wss://fstream.binance.com/ws/',
                'ws_func': self.handle_binance_ws
            },
            'okx': {
                'name': 'OKX',
                'func': self.get_okx_futures,
                'url': 'https://www.okx.com/api/v5/public/instruments',
                'ws_url': 'wss://ws.okx.com:8443/ws/v5/public',
                'ws_func': self.handle_okx_ws
            },
            'bybit': {
                'name': 'Bybit',
                'func': self.get_bybit_futures,
                'url': 'https://api.bybit.com/v5/market/instruments-info',
                'ws_url': 'wss://stream.bybit.com/v5/public/linear',
                'ws_func': self.handle_bybit_ws
            },
            'bitget': {
                'name': 'Bitget',
                'func': self.get_bitget_futures,
                'url': 'https://api.bitget.com/api/v2/mix/market/contracts',
                'ws_url': 'wss://ws.bitget.com/mix/v1/stream',
                'ws_func': self.handle_bitget_ws
            }
        }
        self.futures_data = {}  # 存储原始数据
        self.normalized_data = {}  # 存储标准化数据
        self.symbol_mappings = {}  # 存储符号映射关系
        self.use_cache = use_cache
        self.cache_duration = cache_duration
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # WebSocket相关属性
        self.ws_connections = {}  # 存储WebSocket连接
        self.price_data = {}  # 存储实时价格数据 {exchange: {symbol: {'bid': price, 'ask': price, 'timestamp': time}}}
        self.ws_running = False  # WebSocket运行状态
        self.ws_threads = {}  # WebSocket线程
        self.executor = ThreadPoolExecutor(max_workers=4)  # 线程池

        # 币对标准化映射
        self.symbol_normalizations = {
            # 数量级前缀映射
            '1000': ['1000', '1K'],
            '1000000': ['1000000', '1M'],
            '1000000000': ['1000000000', '1B'],
            # 常见币种别名
            # 'WBTC': 'BTC',
            # 'WETH': 'ETH',
            # 'USDC': 'USDT',  # 在某些分析中可能需要区分，这里先简化
        }

    def normalize_symbol(self, symbol: str) -> str:
        """
        标准化交易对符号

        处理以下情况：
        1. 1000FLOKIUSDT -> FLOKIUSDT (去除数量级前缀)
        2. 1MBABYDOGUSDT -> BABYDOGUSDT (去除数量级前缀)
        3. WBTCUSDT -> BTCUSDT (包装币种映射)

        Args:
            symbol: 原始交易对符号

        Returns:
            标准化后的交易对符号
        """
        if not symbol or not symbol.endswith('USDT'):
            return symbol

        # 提取基础币种（去掉USDT后缀）
        base_symbol = symbol[:-4]  # 去掉'USDT'

        # 处理数量级前缀
        # 匹配模式：数字前缀 + 币种名称
        prefix_pattern = r'^(\d+)([A-Z]+)$'
        match = re.match(prefix_pattern, base_symbol)

        if match:
            prefix, coin = match.groups()
            # 常见的数量级前缀
            if prefix in ['1000', '1000000', '1000000000']:
                base_symbol = coin
            elif prefix == '1' and len(coin) > 2:  # 如1MBABYDOGUSDT中的1M
                # 检查是否是1M, 1K, 1B等前缀
                if coin.startswith('M') and len(coin) > 1:
                    base_symbol = coin[1:]  # 去掉M前缀
                elif coin.startswith('K') and len(coin) > 1:
                    base_symbol = coin[1:]  # 去掉K前缀
                elif coin.startswith('B') and len(coin) > 1:
                    base_symbol = coin[1:]  # 去掉B前缀

        # 处理币种别名映射
        if base_symbol in self.symbol_normalizations:
            base_symbol = self.symbol_normalizations[base_symbol]

        return base_symbol + 'USDT'

    def get_symbol_variants(self, normalized_symbol: str) -> Set[str]:
        """
        获取标准化符号的所有可能变体

        Args:
            normalized_symbol: 标准化的交易对符号

        Returns:
            包含所有可能变体的集合
        """
        if not normalized_symbol.endswith('USDT'):
            return {normalized_symbol}

        base_coin = normalized_symbol[:-4]
        variants = {normalized_symbol}

        # 添加数量级前缀变体
        variants.add(f"1000{base_coin}USDT")
        variants.add(f"1M{base_coin}USDT")
        variants.add(f"1K{base_coin}USDT")
        variants.add(f"1B{base_coin}USDT")
        variants.add(f"1000000{base_coin}USDT")
        variants.add(f"1000000000{base_coin}USDT")

        return variants

    def create_normalized_symbol_mapping(self, symbols: Set[str]) -> Dict[str, Set[str]]:
        """
        创建标准化符号到原始符号的映射

        Args:
            symbols: 原始交易对符号集合

        Returns:
            标准化符号 -> 原始符号集合的映射
        """
        mapping = defaultdict(set)

        for symbol in symbols:
            normalized = self.normalize_symbol(symbol)
            mapping[normalized].add(symbol)

        return dict(mapping)

    def make_request(self, url: str, params: Optional[Dict] = None, timeout: int = 15) -> Optional[Dict]:
        """统一的请求方法"""
        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed for {url}: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error for {url}: {e}")
            return None
        
    def get_binance_futures(self) -> Set[str]:
        """获取币安期货交易对"""
        logger.info("Fetching Binance futures data...")
        data = self.make_request(self.exchanges['binance']['url'])
        
        if not data or 'symbols' not in data:
            logger.warning("Failed to fetch Binance data, using fallback")
            return self._get_fallback_data('binance')
            
        symbols = set()
        for symbol_info in data['symbols']:
            if symbol_info.get('status') == 'TRADING':
                symbols.add(symbol_info['symbol'])
                
        logger.info(f"Binance: {len(symbols)} trading pairs")
        return symbols
    
    def get_okx_futures(self) -> Set[str]:
        """获取OKX期货交易对"""
        logger.info("Fetching OKX futures data...")
        params = {"instType": "SWAP"}
        data = self.make_request(self.exchanges['okx']['url'], params)
        
        if not data or data.get('code') != '0':
            logger.warning("Failed to fetch OKX data, using fallback")
            return self._get_fallback_data('okx')
            
        symbols = set()
        for instrument in data.get('data', []):
            if instrument.get('state') == 'live':
                inst_id = instrument['instId']
                if '-USDT-SWAP' in inst_id:
                    symbol = inst_id.replace('-USDT-SWAP', 'USDT').replace('-', '')
                    symbols.add(symbol)
                    
        logger.info(f"OKX: {len(symbols)} trading pairs")
        return symbols
    
    def get_bybit_futures(self) -> Set[str]:
        """获取Bybit期货交易对"""
        logger.info("Fetching Bybit futures data...")
        params = {"category": "linear"}
        data = self.make_request(self.exchanges['bybit']['url'], params)
        
        if not data or data.get('retCode') != 0:
            logger.warning("Failed to fetch Bybit data, using fallback")
            return self._get_fallback_data('bybit')
            
        symbols = set()
        result = data.get('result', {})
        for instrument in result.get('list', []):
            if instrument.get('status') == 'Trading':
                symbols.add(instrument['symbol'])
                
        logger.info(f"Bybit: {len(symbols)} trading pairs")
        return symbols
    
    def get_bitget_futures(self) -> Set[str]:
        """获取Bitget期货交易对"""
        logger.info("Fetching Bitget futures data...")
        params = {"productType": "usdt-futures"}
        data = self.make_request(self.exchanges['bitget']['url'], params)
        
        if not data or data.get('code') != '00000':
            logger.warning("Failed to fetch Bitget data, using fallback")
            return self._get_fallback_data('bitget')
            
        symbols = set()
        for contract in data.get('data', []):
            if contract.get('supportMarginCoins'):
                symbols.add(contract['symbol'])
                
        logger.info(f"Bitget: {len(symbols)} trading pairs")
        return symbols
    
    def _get_fallback_data(self, exchange: str) -> Set[str]:
        """获取备用数据"""
        fallback_data = {
            'binance': {'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT', 
                       'BNBUSDT', 'LTCUSDT', 'XRPUSDT', 'BCHUSDT', 'EOSUSDT'},
            'okx': {'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'SOLUSDT',
                   'AVAXUSDT', 'MATICUSDT', 'ATOMUSDT', 'NEARUSDT', 'FTMUSDT'},
            'bybit': {'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'XRPUSDT',
                     'DOGEUSDT', 'SHIBUSDT', 'AVAXUSDT', 'TRXUSDT', 'MATICUSDT'},
            'bitget': {'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT',
                      'SOLUSDT', 'DOGEUSDT', 'DOTUSDT', 'AVAXUSDT', 'LINKUSDT'}
        }
        
        data = fallback_data.get(exchange, set())
        logger.info(f"Using fallback data for {exchange}: {len(data)} pairs")
        return data
    
    def fetch_all_data(self, exchanges: Optional[List[str]] = None):
        """获取所有交易所数据"""
        if exchanges is None:
            exchanges = list(self.exchanges.keys())

        logger.info("Starting data collection...")
        logger.info("-" * 50)

        for exchange_name in exchanges:
            if exchange_name not in self.exchanges:
                logger.warning(f"Unknown exchange: {exchange_name}")
                continue

            try:
                fetch_func = self.exchanges[exchange_name]['func']
                raw_symbols = fetch_func()
                self.futures_data[exchange_name] = raw_symbols

                # 创建标准化映射
                self.symbol_mappings[exchange_name] = self.create_normalized_symbol_mapping(raw_symbols)

                # 创建标准化符号集合
                self.normalized_data[exchange_name] = set(self.symbol_mappings[exchange_name].keys())

                logger.info(f"{exchange_name.upper()}: {len(raw_symbols)} raw -> {len(self.normalized_data[exchange_name])} normalized")

                time.sleep(1)  # 避免请求过快
            except Exception as e:
                logger.error(f"Error fetching data for {exchange_name}: {e}")
                raw_symbols = self._get_fallback_data(exchange_name)
                self.futures_data[exchange_name] = raw_symbols
                self.symbol_mappings[exchange_name] = self.create_normalized_symbol_mapping(raw_symbols)
                self.normalized_data[exchange_name] = set(self.symbol_mappings[exchange_name].keys())

        logger.info("-" * 50)
        logger.info("Data collection completed!")

        # 显示标准化统计
        self._show_normalization_stats()

    def _show_normalization_stats(self):
        """显示标准化统计信息"""
        logger.info("\n=== Symbol Normalization Statistics ===")

        for exchange_name in self.futures_data.keys():
            raw_count = len(self.futures_data[exchange_name])
            normalized_count = len(self.normalized_data[exchange_name])

            # 找出被合并的符号示例
            merged_examples = []
            for normalized, originals in self.symbol_mappings[exchange_name].items():
                if len(originals) > 1:
                    merged_examples.append(f"{normalized} <- {', '.join(sorted(originals))}")

            logger.info(f"{exchange_name.upper()}:")
            logger.info(f"  Raw symbols: {raw_count}")
            logger.info(f"  Normalized: {normalized_count}")
            logger.info(f"  Merged groups: {len(merged_examples)}")

            # 显示前几个合并示例
            if merged_examples:
                logger.info("  Examples of merged symbols:")
                for example in merged_examples[:3]:
                    logger.info(f"    {example}")
                if len(merged_examples) > 3:
                    logger.info(f"    ... and {len(merged_examples) - 3} more")
            logger.info("")

    def analyze_data(self) -> Dict:
        """分析数据"""
        if not self.futures_data:
            logger.error("No data available. Please fetch data first.")
            return {}

        analysis_results = {}

        # 基础统计 - 使用标准化数据
        logger.info("\n=== Exchange Statistics (Normalized) ===")
        total_unique_normalized = set()
        for exchange, symbols in self.normalized_data.items():
            count = len(symbols)
            raw_count = len(self.futures_data[exchange])
            logger.info(f"{exchange.upper():>8}: {raw_count:>4} raw -> {count:>4} normalized pairs")
            total_unique_normalized.update(symbols)

        total_count = len(total_unique_normalized)
        logger.info(f"{'TOTAL':>8}: {total_count:>4} unique normalized trading pairs")

        analysis_results['basic_stats'] = {
            'exchanges_raw': {ex: len(symbols) for ex, symbols in self.futures_data.items()},
            'exchanges_normalized': {ex: len(symbols) for ex, symbols in self.normalized_data.items()},
            'total_unique_raw': len(set().union(*self.futures_data.values())),
            'total_unique_normalized': total_count
        }
        
        # 交集分析 - 使用标准化数据
        logger.info("\n=== Intersection Analysis (Normalized) ===")
        exchanges = list(self.normalized_data.keys())
        intersections = {}
        intersections_detailed = {}

        for i, ex1 in enumerate(exchanges):
            for j, ex2 in enumerate(exchanges):
                if i < j:
                    # 标准化符号的交集
                    intersection_normalized = self.normalized_data[ex1] & self.normalized_data[ex2]
                    count = len(intersection_normalized)
                    pct1 = count / len(self.normalized_data[ex1]) * 100
                    pct2 = count / len(self.normalized_data[ex2]) * 100

                    # 获取原始符号的详细映射
                    detailed_mapping = {}
                    for norm_symbol in intersection_normalized:
                        ex1_originals = self.symbol_mappings[ex1].get(norm_symbol, set())
                        ex2_originals = self.symbol_mappings[ex2].get(norm_symbol, set())
                        detailed_mapping[norm_symbol] = {
                            ex1: list(ex1_originals),
                            ex2: list(ex2_originals)
                        }

                    intersections[f"{ex1}_{ex2}"] = {
                        'count': count,
                        'percentage_1': pct1,
                        'percentage_2': pct2,
                        'symbols': intersection_normalized
                    }

                    intersections_detailed[f"{ex1}_{ex2}"] = detailed_mapping

                    logger.info(f"{ex1.upper():>8} ∩ {ex2.upper():<8}: {count:>3} pairs "
                              f"({pct1:.1f}% | {pct2:.1f}%)")

        analysis_results['intersections'] = intersections
        analysis_results['intersections_detailed'] = intersections_detailed
        
        # 独有交易对 - 使用标准化数据
        unique_pairs_normalized = {}
        unique_pairs_detailed = {}

        for exchange, symbols in self.normalized_data.items():
            others_union = set()
            for other_ex, other_symbols in self.normalized_data.items():
                if other_ex != exchange:
                    others_union.update(other_symbols)

            unique_normalized = symbols - others_union
            unique_pairs_normalized[exchange] = unique_normalized

            # 获取独有符号的原始形式
            unique_detailed = {}
            for norm_symbol in unique_normalized:
                originals = self.symbol_mappings[exchange].get(norm_symbol, set())
                unique_detailed[norm_symbol] = list(originals)

            unique_pairs_detailed[exchange] = unique_detailed

        analysis_results['unique_pairs_normalized'] = unique_pairs_normalized
        analysis_results['unique_pairs_detailed'] = unique_pairs_detailed

        # 保留原始数据分析以便对比
        unique_pairs_raw = {}
        for exchange, symbols in self.futures_data.items():
            others_union = set()
            for other_ex, other_symbols in self.futures_data.items():
                if other_ex != exchange:
                    others_union.update(other_symbols)

            unique = symbols - others_union
            unique_pairs_raw[exchange] = unique

        analysis_results['unique_pairs_raw'] = unique_pairs_raw

        return analysis_results
    
    def create_comprehensive_visualization(self, analysis_results: Dict):
        """创建综合可视化"""
        plt.style.use('default')
        fig = plt.figure(figsize=(20, 16))
        
        exchanges = list(self.normalized_data.keys())
        exchange_names = [self.exchanges[ex]['name'] for ex in exchanges]
        colors = ['#f7931e', '#00d4ff', '#f7b500', '#00c896']

        # 1. 交易对数量柱状图 (显示原始和标准化数据)
        ax1 = plt.subplot(3, 3, 1)
        raw_counts = [len(self.futures_data[ex]) for ex in exchanges]
        normalized_counts = [len(self.normalized_data[ex]) for ex in exchanges]

        x = range(len(exchanges))
        width = 0.35

        bars1 = ax1.bar([i - width/2 for i in x], raw_counts, width,
                       label='Raw', color=colors, alpha=0.7, edgecolor='black')
        bars2 = ax1.bar([i + width/2 for i in x], normalized_counts, width,
                       label='Normalized', color=colors, alpha=1.0, edgecolor='black')

        ax1.set_title('Trading Pairs Count by Exchange', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Number of Trading Pairs')
        ax1.set_xticks(x)
        ax1.set_xticklabels(exchange_names)
        ax1.legend()
        ax1.grid(axis='y', alpha=0.3)

        # 添加数值标签
        for i, (raw, norm) in enumerate(zip(raw_counts, normalized_counts)):
            ax1.text(i - width/2, raw + max(raw_counts)*0.01, str(raw),
                    ha='center', va='bottom', fontweight='bold', fontsize=9)
            ax1.text(i + width/2, norm + max(normalized_counts)*0.01, str(norm),
                    ha='center', va='bottom', fontweight='bold', fontsize=9)
        
        # 2. 交集热力图 (使用标准化数据)
        ax2 = plt.subplot(3, 3, 2)
        matrix_data = []
        for ex1 in exchanges:
            row = []
            for ex2 in exchanges:
                if ex1 == ex2:
                    intersection = len(self.normalized_data[ex1])
                else:
                    intersection = len(self.normalized_data[ex1] & self.normalized_data[ex2])
                row.append(intersection)
            matrix_data.append(row)

        df_matrix = pd.DataFrame(matrix_data, index=exchange_names, columns=exchange_names)
        sns.heatmap(df_matrix, annot=True, fmt='d', cmap='YlOrRd', square=True, ax=ax2)
        ax2.set_title('Common Trading Pairs Heatmap (Normalized)', fontsize=14, fontweight='bold')

        # 3. 独有交易对 (使用标准化数据)
        ax3 = plt.subplot(3, 3, 3)
        unique_counts = [len(analysis_results['unique_pairs_normalized'][ex]) for ex in exchanges]
        bars = ax3.bar(exchange_names, unique_counts, color=colors, alpha=0.8, edgecolor='black')
        ax3.set_title('Unique Trading Pairs', fontsize=14, fontweight='bold')
        ax3.set_ylabel('Number of Unique Pairs')
        ax3.grid(axis='y', alpha=0.3)
        
        for bar, count in zip(bars, unique_counts):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(unique_counts)*0.02,
                    str(count), ha='center', va='bottom', fontweight='bold')
        
        # 4. 市场覆盖度
        ax4 = plt.subplot(3, 3, 4)
        total_unique = analysis_results['basic_stats']['total_unique_normalized']
        coverage_data = [count / total_unique * 100 for count in normalized_counts]
        bars = ax4.bar(exchange_names, coverage_data, color=colors, alpha=0.8, edgecolor='black')
        ax4.set_title('Market Coverage', fontsize=14, fontweight='bold')
        ax4.set_ylabel('Coverage Percentage (%)')
        ax4.grid(axis='y', alpha=0.3)
        
        for bar, coverage in zip(bars, coverage_data):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(coverage_data)*0.01,
                    f'{coverage:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 5. 交集比例矩阵 (使用标准化数据)
        ax5 = plt.subplot(3, 3, 5)
        percentage_matrix = []
        for ex1 in exchanges:
            row = []
            for ex2 in exchanges:
                if ex1 == ex2:
                    percentage = 100.0
                else:
                    intersection = len(self.normalized_data[ex1] & self.normalized_data[ex2])
                    percentage = intersection / len(self.normalized_data[ex1]) * 100
                row.append(percentage)
            percentage_matrix.append(row)
        
        df_percentage = pd.DataFrame(percentage_matrix, index=exchange_names, columns=exchange_names)
        sns.heatmap(df_percentage, annot=True, fmt='.1f', cmap='Blues', square=True, ax=ax5)
        ax5.set_title('Overlap Percentage Matrix', fontsize=14, fontweight='bold')
        
        # 6. 韦恩图统计 (使用标准化数据)
        ax6 = plt.subplot(3, 3, 6)
        common_all = set.intersection(*[self.normalized_data[ex] for ex in exchanges])

        overlap_stats = {'All Exchanges': len(common_all)}
        for ex in exchanges:
            unique_count = len(analysis_results['unique_pairs_normalized'][ex])
            if unique_count > 0:
                overlap_stats[f'Only {self.exchanges[ex]["name"]}'] = unique_count
        
        if overlap_stats:
            labels = list(overlap_stats.keys())
            sizes = list(overlap_stats.values())
            colors_pie = plt.cm.Set3(range(len(labels)))
            
            ax6.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors_pie, startangle=90)
            ax6.set_title('Trading Pairs Distribution', fontsize=14, fontweight='bold')
        
        # 7. 时间序列模拟（示例）- 使用标准化数据
        ax7 = plt.subplot(3, 3, 7)
        dates = pd.date_range('2024-01-01', periods=12, freq='M')
        for i, (ex, color) in enumerate(zip(exchanges, colors)):
            # 模拟数据增长
            base_count = len(self.normalized_data[ex])
            growth = [base_count * (1 + 0.02 * j + 0.01 * i) for j in range(12)]
            ax7.plot(dates, growth, color=color, marker='o', label=self.exchanges[ex]['name'])
        
        ax7.set_title('Trading Pairs Growth Trend (Simulated)', fontsize=14, fontweight='bold')
        ax7.set_ylabel('Number of Trading Pairs')
        ax7.legend()
        ax7.grid(True, alpha=0.3)
        
        # 8. 交集关系网络图 (使用标准化数据)
        ax8 = plt.subplot(3, 3, 8)
        # 创建简单的网络图
        import numpy as np

        n_exchanges = len(exchanges)
        angles = np.linspace(0, 2*np.pi, n_exchanges, endpoint=False)

        # 绘制节点
        for i, (angle, ex) in enumerate(zip(angles, exchanges)):
            x, y = np.cos(angle), np.sin(angle)
            ax8.scatter(x, y, s=normalized_counts[i]*2, c=colors[i], alpha=0.7, edgecolors='black')
            ax8.text(x*1.2, y*1.2, self.exchanges[ex]['name'], ha='center', va='center', fontweight='bold')

        # 绘制连接线
        for i, ex1 in enumerate(exchanges):
            for j, ex2 in enumerate(exchanges):
                if i < j:
                    intersection = len(self.normalized_data[ex1] & self.normalized_data[ex2])
                    if intersection > 0:
                        x1, y1 = np.cos(angles[i]), np.sin(angles[i])
                        x2, y2 = np.cos(angles[j]), np.sin(angles[j])
                        line_width = intersection / max(normalized_counts) * 5
                        ax8.plot([x1, x2], [y1, y2], 'gray', alpha=0.5, linewidth=line_width)
        
        ax8.set_xlim(-1.5, 1.5)
        ax8.set_ylim(-1.5, 1.5)
        ax8.set_aspect('equal')
        ax8.set_title('Exchange Network (Node size = Pairs count)', fontsize=14, fontweight='bold')
        ax8.axis('off')
        
        # 9. 总结统计 (使用标准化数据)
        ax9 = plt.subplot(3, 3, 9)
        ax9.axis('off')

        summary_text = f"""
        📊 SUMMARY STATISTICS (Normalized)

        Total Unique Pairs: {total_unique}

        Exchange Rankings:
        """

        # 按交易对数量排序
        sorted_exchanges = sorted(exchanges, key=lambda x: len(self.normalized_data[x]), reverse=True)
        for i, ex in enumerate(sorted_exchanges, 1):
            count = len(self.normalized_data[ex])
            raw_count = len(self.futures_data[ex])
            coverage = count / total_unique * 100
            summary_text += f"\n{i}. {self.exchanges[ex]['name']}: {count} pairs ({raw_count} raw, {coverage:.1f}%)"

        common_all_count = len(common_all)
        summary_text += f"\n\nCommon to all: {common_all_count} pairs"
        
        ax9.text(0.05, 0.95, summary_text, transform=ax9.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout(pad=3.0)
        plt.show()
    
    def generate_report(self, analysis_results: Dict, output_file: str = None):
        """生成详细报告"""
        report = []
        report.append("="*80)
        report.append("            FUTURES TRADING PAIRS ANALYSIS REPORT")
        report.append("="*80)
        report.append(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 基础统计
        report.append("📊 BASIC STATISTICS")
        report.append("-" * 50)
        exchanges = list(self.futures_data.keys())
        total_unique_raw = analysis_results['basic_stats']['total_unique_raw']
        total_unique_normalized = analysis_results['basic_stats']['total_unique_normalized']

        report.append("Raw Data:")
        for ex in exchanges:
            count = len(self.futures_data[ex])
            coverage = count / total_unique_raw * 100
            report.append(f"  {self.exchanges[ex]['name']:>10}: {count:>4} pairs ({coverage:>5.1f}% coverage)")

        report.append(f"  {'TOTAL':>10}: {total_unique_raw:>4} unique pairs")
        report.append("")

        report.append("Normalized Data:")
        for ex in exchanges:
            count = len(self.normalized_data[ex])
            coverage = count / total_unique_normalized * 100
            merged_count = len(self.futures_data[ex]) - count
            report.append(f"  {self.exchanges[ex]['name']:>10}: {count:>4} pairs ({coverage:>5.1f}% coverage, {merged_count} merged)")

        report.append(f"  {'TOTAL':>10}: {total_unique_normalized:>4} unique pairs")
        report.append("")
        
        # 交集分析
        report.append("🔄 INTERSECTION ANALYSIS")
        report.append("-" * 50)
        for key, data in analysis_results['intersections'].items():
            ex1, ex2 = key.split('_')
            report.append(f"{self.exchanges[ex1]['name']} ∩ {self.exchanges[ex2]['name']}: "
                         f"{data['count']} pairs ({data['percentage_1']:.1f}% | {data['percentage_2']:.1f}%)")
        report.append("")
        
        # 独有交易对 (标准化数据)
        report.append("🏢 UNIQUE TRADING PAIRS (Normalized)")
        report.append("-" * 50)
        for ex, unique_pairs in analysis_results['unique_pairs_normalized'].items():
            unique_detailed = analysis_results['unique_pairs_detailed'][ex]
            report.append(f"\n{self.exchanges[ex]['name']} EXCLUSIVE ({len(unique_pairs)} normalized pairs):")
            if unique_pairs:
                sorted_unique = sorted(list(unique_pairs))
                for i, symbol in enumerate(sorted_unique[:10], 1):
                    originals = unique_detailed.get(symbol, [])
                    if len(originals) > 1:
                        report.append(f"  {i:2d}. {symbol} (from: {', '.join(originals)})")
                    else:
                        report.append(f"  {i:2d}. {symbol}")
                if len(sorted_unique) > 10:
                    report.append(f"      ... and {len(sorted_unique) - 10} more pairs")
            else:
                report.append("  No exclusive pairs")
        
        # 共同交易对 (标准化数据)
        common_all = set.intersection(*[self.normalized_data[ex] for ex in exchanges])
        report.append(f"\n📈 COMMON TO ALL EXCHANGES ({len(common_all)} normalized pairs):")
        report.append("-" * 50)
        if common_all:
            sorted_common = sorted(list(common_all))
            for i, symbol in enumerate(sorted_common[:20], 1):
                # 显示每个交易所的原始符号形式
                variants_info = []
                for ex in exchanges:
                    originals = self.symbol_mappings[ex].get(symbol, set())
                    if originals:
                        variants_info.append(f"{ex}:{','.join(sorted(originals))}")

                if len(set(len(self.symbol_mappings[ex].get(symbol, set())) for ex in exchanges)) > 1:
                    # 如果不同交易所有不同的原始形式，显示详细信息
                    report.append(f"  {i:2d}. {symbol}")
                    for variant in variants_info:
                        report.append(f"      {variant}")
                else:
                    report.append(f"  {i:2d}. {symbol}")

            if len(sorted_common) > 20:
                report.append(f"      ... and {len(sorted_common) - 20} more pairs")
        else:
            report.append("  No pairs common to all exchanges")

        # 标准化示例
        report.append(f"\n🔄 SYMBOL NORMALIZATION EXAMPLES:")
        report.append("-" * 50)
        normalization_examples = []
        for ex in exchanges:
            for normalized, originals in self.symbol_mappings[ex].items():
                if len(originals) > 1:
                    normalization_examples.append((ex, normalized, originals))

        if normalization_examples:
            # 显示前10个示例
            for i, (ex, normalized, originals) in enumerate(normalization_examples[:10], 1):
                report.append(f"  {i:2d}. {self.exchanges[ex]['name']}: {', '.join(sorted(originals))} → {normalized}")
            if len(normalization_examples) > 10:
                report.append(f"      ... and {len(normalization_examples) - 10} more examples")
        else:
            report.append("  No symbol normalization performed")

        report.append("")
        report.append("="*80)
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            logger.info(f"Report saved to {output_file}")
        
        return report_text
    
    def export_data(self, filename: str = "futures_data.json"):
        """导出数据到JSON文件"""
        export_data = {}
        for exchange, symbols in self.futures_data.items():
            export_data[exchange] = {
                'name': self.exchanges[exchange]['name'],
                'count': len(symbols),
                'symbols': sorted(list(symbols))
            }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Data exported to {filename}")

    # WebSocket相关方法
    def start_websocket_streams(self, exchanges: Optional[List[str]] = None, max_symbols_per_exchange: int = 50):
        """启动WebSocket数据流"""
        if exchanges is None:
            exchanges = list(self.exchanges.keys())

        if not self.futures_data:
            logger.error("No futures data available. Please fetch data first.")
            return

        self.ws_running = True
        logger.info("Starting WebSocket streams...")

        # 初始化价格数据存储
        for exchange in exchanges:
            self.price_data[exchange] = {}

        # 为每个交易所启动WebSocket连接
        for exchange in exchanges:
            if exchange not in self.exchanges:
                logger.warning(f"Unknown exchange: {exchange}")
                continue

            # 获取该交易所的USDT交易对（限制数量以避免过多连接）
            usdt_symbols = [s for s in self.futures_data[exchange] if s.endswith('USDT')]
            selected_symbols = sorted(usdt_symbols)[:max_symbols_per_exchange]

            logger.info(f"Starting WebSocket for {exchange} with {len(selected_symbols)} symbols")

            # 在线程池中启动WebSocket连接
            future = self.executor.submit(self._start_exchange_websocket, exchange, selected_symbols)
            self.ws_threads[exchange] = future

        logger.info("All WebSocket streams started")

    def _start_exchange_websocket(self, exchange: str, symbols: List[str]):
        """为单个交易所启动WebSocket连接"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 运行WebSocket连接
            ws_func = self.exchanges[exchange]['ws_func']
            loop.run_until_complete(ws_func(exchange, symbols))
        except Exception as e:
            logger.error(f"WebSocket error for {exchange}: {e}")
        finally:
            loop.close()

    async def handle_binance_ws(self, exchange: str, symbols: List[str]):
        """处理币安WebSocket连接"""
        # 币安支持单个连接订阅多个交易对
        streams = [f"{symbol.lower()}@bookTicker" for symbol in symbols]
        stream_names = "/".join(streams)
        ws_url = f"{self.exchanges[exchange]['ws_url']}{stream_names}"

        try:
            async with websockets.connect(ws_url) as websocket:
                logger.info(f"Connected to Binance WebSocket for {len(symbols)} symbols")

                while self.ws_running:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(message)

                        if 'stream' in data and 'data' in data:
                            stream_data = data['data']
                            symbol = stream_data.get('s', '').upper()

                            if symbol in symbols:
                                self.price_data[exchange][symbol] = {
                                    'bid': float(stream_data.get('b', 0)),
                                    'ask': float(stream_data.get('a', 0)),
                                    'timestamp': time.time()
                                }

                    except asyncio.TimeoutError:
                        # 发送ping保持连接
                        await websocket.ping()
                    except Exception as e:
                        logger.error(f"Binance WebSocket message error: {e}")
                        break

        except Exception as e:
            logger.error(f"Binance WebSocket connection error: {e}")

    async def handle_okx_ws(self, exchange: str, symbols: List[str]):
        """处理OKX WebSocket连接"""
        ws_url = self.exchanges[exchange]['ws_url']

        try:
            async with websockets.connect(ws_url) as websocket:
                logger.info(f"Connected to OKX WebSocket for {len(symbols)} symbols")

                # 订阅交易对
                for symbol in symbols:
                    # OKX期货合约格式转换
                    inst_id = f"{symbol[:-4]}-USDT-SWAP"  # BTCUSDT -> BTC-USDT-SWAP
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [{
                            "channel": "books5",
                            "instId": inst_id
                        }]
                    }
                    await websocket.send(json.dumps(subscribe_msg))

                while self.ws_running:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(message)

                        if 'data' in data:
                            for item in data['data']:
                                inst_id = item.get('instId', '')
                                if '-USDT-SWAP' in inst_id:
                                    symbol = inst_id.replace('-USDT-SWAP', 'USDT').replace('-', '')

                                    bids = item.get('bids', [])
                                    asks = item.get('asks', [])

                                    if bids and asks and symbol in symbols:
                                        self.price_data[exchange][symbol] = {
                                            'bid': float(bids[0][0]),
                                            'ask': float(asks[0][0]),
                                            'timestamp': time.time()
                                        }

                    except asyncio.TimeoutError:
                        # 发送ping保持连接
                        ping_msg = {"op": "ping"}
                        await websocket.send(json.dumps(ping_msg))
                    except Exception as e:
                        logger.error(f"OKX WebSocket message error: {e}")
                        break

        except Exception as e:
            logger.error(f"OKX WebSocket connection error: {e}")

    async def handle_bybit_ws(self, exchange: str, symbols: List[str]):
        """处理Bybit WebSocket连接"""
        ws_url = self.exchanges[exchange]['ws_url']

        try:
            async with websockets.connect(ws_url) as websocket:
                logger.info(f"Connected to Bybit WebSocket for {len(symbols)} symbols")

                # 订阅交易对
                topics = [f"orderbook.1.{symbol}" for symbol in symbols]
                subscribe_msg = {
                    "op": "subscribe",
                    "args": topics
                }
                await websocket.send(json.dumps(subscribe_msg))

                while self.ws_running:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(message)

                        if 'data' in data and 'topic' in data:
                            topic = data['topic']
                            if 'orderbook.1.' in topic:
                                symbol = topic.replace('orderbook.1.', '')

                                order_data = data['data']
                                bids = order_data.get('b', [])
                                asks = order_data.get('a', [])

                                if bids and asks and symbol in symbols:
                                    self.price_data[exchange][symbol] = {
                                        'bid': float(bids[0][0]),
                                        'ask': float(asks[0][0]),
                                        'timestamp': time.time()
                                    }

                    except asyncio.TimeoutError:
                        # 发送ping保持连接
                        ping_msg = {"op": "ping"}
                        await websocket.send(json.dumps(ping_msg))
                    except Exception as e:
                        logger.error(f"Bybit WebSocket message error: {e}")
                        break

        except Exception as e:
            logger.error(f"Bybit WebSocket connection error: {e}")

    async def handle_bitget_ws(self, exchange: str, symbols: List[str]):
        """处理Bitget WebSocket连接"""
        ws_url = self.exchanges[exchange]['ws_url']

        try:
            async with websockets.connect(ws_url) as websocket:
                logger.info(f"Connected to Bitget WebSocket for {len(symbols)} symbols")

                # 订阅交易对
                for symbol in symbols:
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [{
                            "instType": "mc",
                            "channel": "books5",
                            "instId": symbol
                        }]
                    }
                    await websocket.send(json.dumps(subscribe_msg))

                while self.ws_running:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(message)

                        if 'data' in data:
                            for item in data['data']:
                                symbol = item.get('instId', '')
                                bids = item.get('bids', [])
                                asks = item.get('asks', [])

                                if bids and asks and symbol in symbols:
                                    self.price_data[exchange][symbol] = {
                                        'bid': float(bids[0][0]),
                                        'ask': float(asks[0][0]),
                                        'timestamp': time.time()
                                    }

                    except asyncio.TimeoutError:
                        # 发送ping保持连接
                        ping_msg = {"op": "ping"}
                        await websocket.send(json.dumps(ping_msg))
                    except Exception as e:
                        logger.error(f"Bitget WebSocket message error: {e}")
                        break

        except Exception as e:
            logger.error(f"Bitget WebSocket connection error: {e}")

    def stop_websocket_streams(self):
        """停止WebSocket数据流"""
        logger.info("Stopping WebSocket streams...")
        self.ws_running = False

        # 等待所有线程完成
        for exchange, future in self.ws_threads.items():
            try:
                future.result(timeout=5.0)
            except Exception as e:
                logger.warning(f"Error stopping WebSocket for {exchange}: {e}")

        self.ws_threads.clear()
        logger.info("All WebSocket streams stopped")

    def get_current_prices(self, exchange: Optional[str] = None) -> Dict:
        """获取当前价格数据"""
        if exchange:
            return self.price_data.get(exchange, {})
        return self.price_data.copy()

    def get_price_summary(self) -> Dict:
        """获取价格数据摘要"""
        summary = {}
        current_time = time.time()

        for exchange, symbols_data in self.price_data.items():
            active_symbols = 0
            total_symbols = len(symbols_data)
            latest_update = 0

            for symbol, price_info in symbols_data.items():
                if current_time - price_info['timestamp'] < 60:  # 1分钟内的数据认为是活跃的
                    active_symbols += 1
                latest_update = max(latest_update, price_info['timestamp'])

            summary[exchange] = {
                'total_symbols': total_symbols,
                'active_symbols': active_symbols,
                'latest_update': datetime.fromtimestamp(latest_update).strftime('%Y-%m-%d %H:%M:%S') if latest_update > 0 else 'N/A',
                'status': 'Active' if active_symbols > 0 else 'Inactive'
            }

        return summary

    def print_price_summary(self):
        """打印价格数据摘要"""
        summary = self.get_price_summary()

        print("\n" + "="*80)
        print("                    REAL-TIME PRICE DATA SUMMARY")
        print("="*80)

        for exchange, info in summary.items():
            print(f"{self.exchanges[exchange]['name']:>10}: {info['active_symbols']:>3}/{info['total_symbols']:<3} active | "
                  f"Status: {info['status']:<8} | Last: {info['latest_update']}")

        print("="*80)

    def get_arbitrage_opportunities(self, min_spread_pct: float = 0.1) -> List[Dict]:
        """寻找套利机会"""
        opportunities = []
        current_time = time.time()

        # 收集所有交易所的活跃价格数据
        active_prices = {}
        for exchange, symbols_data in self.price_data.items():
            active_prices[exchange] = {}
            for symbol, price_info in symbols_data.items():
                if current_time - price_info['timestamp'] < 60:  # 1分钟内的数据
                    active_prices[exchange][symbol] = price_info

        # 寻找共同交易对的价格差异
        all_symbols = set()
        for symbols_data in active_prices.values():
            all_symbols.update(symbols_data.keys())

        for symbol in all_symbols:
            exchanges_with_symbol = []
            for exchange, symbols_data in active_prices.items():
                if symbol in symbols_data:
                    exchanges_with_symbol.append((exchange, symbols_data[symbol]))

            if len(exchanges_with_symbol) >= 2:
                # 找到最高买价和最低卖价
                best_bid = max(exchanges_with_symbol, key=lambda x: x[1]['bid'])
                best_ask = min(exchanges_with_symbol, key=lambda x: x[1]['ask'])

                if best_bid[0] != best_ask[0]:  # 不同交易所
                    spread = best_bid[1]['bid'] - best_ask[1]['ask']
                    spread_pct = (spread / best_ask[1]['ask']) * 100

                    if spread_pct >= min_spread_pct:
                        opportunities.append({
                            'symbol': symbol,
                            'buy_exchange': best_ask[0],
                            'sell_exchange': best_bid[0],
                            'buy_price': best_ask[1]['ask'],
                            'sell_price': best_bid[1]['bid'],
                            'spread': spread,
                            'spread_pct': spread_pct,
                            'timestamp': current_time
                        })

        # 按收益率排序
        opportunities.sort(key=lambda x: x['spread_pct'], reverse=True)
        return opportunities

    def print_arbitrage_opportunities(self, min_spread_pct: float = 0.1, max_results: int = 10):
        """打印套利机会"""
        opportunities = self.get_arbitrage_opportunities(min_spread_pct)

        print(f"\n" + "="*100)
        print(f"                    ARBITRAGE OPPORTUNITIES (Min Spread: {min_spread_pct}%)")
        print("="*100)

        if not opportunities:
            print("No arbitrage opportunities found with the specified criteria.")
            print("="*100)
            return

        print(f"{'Symbol':<15} {'Buy@':<10} {'Sell@':<10} {'Buy Price':<12} {'Sell Price':<12} {'Spread':<10} {'Spread%':<8}")
        print("-"*100)

        for i, opp in enumerate(opportunities[:max_results]):
            print(f"{opp['symbol']:<15} {self.exchanges[opp['buy_exchange']]['name']:<10} "
                  f"{self.exchanges[opp['sell_exchange']]['name']:<10} "
                  f"{opp['buy_price']:<12.6f} {opp['sell_price']:<12.6f} "
                  f"{opp['spread']:<10.6f} {opp['spread_pct']:<8.2f}%")

        if len(opportunities) > max_results:
            print(f"... and {len(opportunities) - max_results} more opportunities")

        print("="*100)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Futures Trading Pairs Analyzer with Real-time WebSocket Support')
    parser.add_argument('--exchanges', nargs='+',
                       choices=['binance', 'okx', 'bybit', 'bitget'],
                       default=['binance', 'okx', 'bybit', 'bitget'],
                       help='Exchanges to analyze')
    parser.add_argument('--output', '-o', type=str,
                       help='Output file for report')
    parser.add_argument('--export', '-e', type=str,
                       help='Export data to JSON file')
    parser.add_argument('--no-plot', action='store_true',
                       help='Skip visualization')
    parser.add_argument('--websocket', '-w', action='store_true',
                       help='Enable real-time WebSocket price streaming')
    parser.add_argument('--max-symbols', type=int, default=50,
                       help='Maximum symbols per exchange for WebSocket (default: 50)')
    parser.add_argument('--arbitrage', '-a', action='store_true',
                       help='Show arbitrage opportunities (requires --websocket)')
    parser.add_argument('--min-spread', type=float, default=0.1,
                       help='Minimum spread percentage for arbitrage (default: 0.1)')
    parser.add_argument('--monitor-time', type=int, default=60,
                       help='WebSocket monitoring time in seconds (default: 60)')

    args = parser.parse_args()

    # 创建分析器
    analyzer = FuturesAnalyzer()

    try:
        # 获取数据
        analyzer.fetch_all_data(args.exchanges)

        # 分析数据
        analysis_results = analyzer.analyze_data()

        if not args.no_plot:
            # 创建可视化
            analyzer.create_comprehensive_visualization(analysis_results)

        # 生成报告
        report = analyzer.generate_report(analysis_results, args.output)
        print(report)

        # 导出数据
        if args.export:
            analyzer.export_data(args.export)

        # WebSocket实时数据流
        if args.websocket:
            print("\n" + "="*80)
            print("                    STARTING REAL-TIME PRICE MONITORING")
            print("="*80)

            # 启动WebSocket连接
            analyzer.start_websocket_streams(args.exchanges, args.max_symbols)

            try:
                # 监控指定时间
                monitor_start = time.time()
                last_summary_time = 0
                last_arbitrage_time = 0

                while time.time() - monitor_start < args.monitor_time:
                    current_time = time.time()

                    # 每10秒显示一次摘要
                    if current_time - last_summary_time >= 10:
                        analyzer.print_price_summary()
                        last_summary_time = current_time

                    # 每30秒检查套利机会
                    if args.arbitrage and current_time - last_arbitrage_time >= 30:
                        analyzer.print_arbitrage_opportunities(args.min_spread)
                        last_arbitrage_time = current_time

                    time.sleep(1)

            except KeyboardInterrupt:
                logger.info("WebSocket monitoring interrupted by user")
            finally:
                # 停止WebSocket连接
                analyzer.stop_websocket_streams()

                # 显示最终统计
                print("\n" + "="*80)
                print("                    FINAL STATISTICS")
                print("="*80)
                analyzer.print_price_summary()

                if args.arbitrage:
                    analyzer.print_arbitrage_opportunities(args.min_spread)

    except KeyboardInterrupt:
        logger.info("Analysis interrupted by user")
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()