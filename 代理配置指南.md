# WebSocket代理配置指南

## 🌐 为什么需要代理

由于地区网络限制，某些交易所的WebSocket连接可能无法直接访问。通过配置代理，可以绕过这些限制，实现对所有交易所的WebSocket连接。

## 📋 支持的代理类型

### 1. HTTP/HTTPS代理
- **格式**: `http://IP:端口` 或 `https://IP:端口`
- **示例**: `http://127.0.0.1:7890`
- **适用场景**: 大多数代理服务

### 2. SOCKS代理
- **格式**: `socks5://IP:端口` 或 `socks4://IP:端口`
- **示例**: `socks5://127.0.0.1:1080`
- **适用场景**: 更高级的代理需求

## 🔧 常见代理软件配置

### Clash
```
HTTP代理: http://127.0.0.1:7890
SOCKS5代理: socks5://127.0.0.1:7891
```

### V2Ray/V2RayN
```
HTTP代理: http://127.0.0.1:10809
SOCKS5代理: socks5://127.0.0.1:10808
```

### Shadowsocks
```
SOCKS5代理: socks5://127.0.0.1:1080
```

### 其他代理软件
请查看软件设置中的本地代理端口配置

## 🚀 使用方法

### 1. 基础代理配置
```bash
# 使用HTTP代理
python 期货交易所交易对分析工具.py --proxy http://127.0.0.1:7890 --websocket

# 使用SOCKS5代理
python 期货交易所交易对分析工具.py --proxy socks5://127.0.0.1:1080 --websocket
```

### 2. 带认证的代理
```bash
# 如果代理需要用户名和密码
python 期货交易所交易对分析工具.py --proxy http://127.0.0.1:7890 --proxy-auth username:password --websocket
```

### 3. 完整示例
```bash
# 使用代理进行完整的实时监控和套利分析
python 期货交易所交易对分析工具.py \
    --proxy http://127.0.0.1:7890 \
    --websocket \
    --arbitrage \
    --monitor-time 300 \
    --max-symbols 30
```

## 🧪 测试代理连接

### 1. 运行代理测试脚本
```bash
python proxy_test.py
```

### 2. 手动测试特定代理
```bash
# 测试基础连通性
python proxy_test.py
# 然后输入你的代理URL，如: http://127.0.0.1:7890
```

## 📦 安装代理支持依赖

```bash
# 安装HTTP代理支持
pip install aiohttp

# 安装SOCKS代理支持
pip install python-socks

# 或者安装所有依赖
pip install -r requirements.txt
```

## ⚙️ 代理配置步骤

### 步骤1: 启动代理软件
确保你的代理软件（如Clash、V2Ray等）正在运行

### 步骤2: 获取代理端口
查看代理软件的设置，找到本地HTTP或SOCKS代理端口

### 步骤3: 测试代理
```bash
python proxy_test.py
```

### 步骤4: 运行程序
```bash
python 期货交易所交易对分析工具.py --proxy http://127.0.0.1:7890 --websocket
```

## 🔍 故障排除

### 问题1: 代理连接失败
**可能原因**:
- 代理软件未启动
- 端口号错误
- 代理URL格式错误

**解决方案**:
1. 检查代理软件是否运行
2. 确认端口号正确
3. 使用`proxy_test.py`测试连接

### 问题2: 部分交易所仍无法连接
**可能原因**:
- 代理服务器地区限制
- 交易所特殊的连接要求

**解决方案**:
1. 尝试不同的代理服务器
2. 使用不同类型的代理（HTTP vs SOCKS）

### 问题3: 连接速度慢
**可能原因**:
- 代理服务器延迟高
- 网络质量问题

**解决方案**:
1. 选择延迟更低的代理服务器
2. 调整`--max-symbols`参数减少连接数

## 📊 代理效果验证

成功配置代理后，你应该能看到：

```
2025-08-05 15:00:00,000 - INFO - Proxy configured: http://127.0.0.1:7890
2025-08-05 15:00:05,000 - INFO - ✅ binance WebSocket available
2025-08-05 15:00:05,000 - INFO - ✅ okx WebSocket available  
2025-08-05 15:00:05,000 - INFO - ✅ bybit WebSocket available
2025-08-05 15:00:05,000 - INFO - Available WebSocket exchanges: ['binance', 'okx', 'bybit']
```

## 🎯 最佳实践

### 1. 代理选择
- 优先使用HTTP代理（兼容性更好）
- 选择延迟低的代理服务器
- 确保代理服务器稳定可靠

### 2. 参数配置
```bash
# 推荐配置
python 期货交易所交易对分析工具.py \
    --proxy http://127.0.0.1:7890 \
    --websocket \
    --arbitrage \
    --monitor-time 600 \
    --max-symbols 20 \
    --min-spread 0.1
```

### 3. 监控建议
- 开始时使用较短的监控时间测试
- 逐步增加监控的交易对数量
- 定期检查连接状态

## 🔒 安全注意事项

1. **代理安全**: 确保使用可信的代理服务
2. **数据保护**: 避免在不安全的网络环境下使用
3. **访问合规**: 遵守当地法律法规

## 📞 技术支持

如果遇到代理配置问题：

1. 首先运行`python proxy_test.py`进行诊断
2. 检查代理软件日志
3. 尝试不同的代理配置
4. 查看程序运行日志获取详细错误信息

## 🎉 成功示例

配置成功后，你将能够：
- 连接所有支持的交易所WebSocket
- 实时监控更多交易对的价格变化
- 发现更多跨交易所套利机会
- 获得更完整的市场数据分析

---

**提示**: 代理配置成功后，建议保存配置以便后续使用。你可以创建一个批处理文件或shell脚本来快速启动带代理的监控程序。
