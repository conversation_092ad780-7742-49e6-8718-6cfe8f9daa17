#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的WebSocket连接测试 - 测试所有交易所
"""

import asyncio
import websockets
import json
import time

async def test_binance_connection():
    """测试币安WebSocket连接"""
    print("测试币安WebSocket连接...")

    try:
        # 尝试多个可能的URL
        urls_to_try = [
            "wss://fstream.binance.com/ws/btcusdt@bookTicker",
            "wss://stream.binance.com:9443/ws/btcusdt@bookTicker",
            "wss://fstream.binance.com/stream?streams=btcusdt@bookTicker"
        ]

        for i, ws_url in enumerate(urls_to_try):
            try:
                print(f"  尝试URL {i+1}: {ws_url}")
                async with websockets.connect(ws_url, ping_interval=20, ping_timeout=10) as websocket:
                    print("✅ 币安WebSocket连接成功")

                    # 接收几条消息
                    for j in range(3):
                        message = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                        data = json.loads(message)

                        # 处理不同格式的响应
                        if 'stream' in data and 'data' in data:
                            stream_data = data['data']
                            print(f"    消息 {j+1}: {stream_data.get('s')} - Bid: {stream_data.get('b')}, Ask: {stream_data.get('a')}")
                        elif 's' in data:
                            print(f"    消息 {j+1}: {data.get('s')} - Bid: {data.get('b')}, Ask: {data.get('a')}")
                        else:
                            print(f"    消息 {j+1}: {data}")

                        if j < 2:
                            await asyncio.sleep(1)

                    print("✅ 币安WebSocket数据接收正常")
                    return True

            except Exception as e:
                print(f"    URL {i+1} 失败: {e}")
                continue

        print("❌ 所有币安WebSocket URL都连接失败")
        return False

    except Exception as e:
        print(f"❌ 币安WebSocket连接失败: {e}")
        return False

async def test_okx_connection():
    """测试OKX WebSocket连接"""
    print("\n测试OKX WebSocket连接...")
    
    try:
        ws_url = "wss://ws.okx.com:8443/ws/v5/public"
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ OKX WebSocket连接成功")
            
            # 订阅BTC-USDT-SWAP的orderbook
            subscribe_msg = {
                "op": "subscribe",
                "args": [{
                    "channel": "books5",
                    "instId": "BTC-USDT-SWAP"
                }]
            }
            
            await websocket.send(json.dumps(subscribe_msg))
            
            # 接收几条消息
            for i in range(5):  # OKX可能先发送确认消息
                message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                data = json.loads(message)
                
                if 'data' in data and data['data']:
                    item = data['data'][0]
                    bids = item.get('bids', [])
                    asks = item.get('asks', [])
                    
                    if bids and asks:
                        print(f"  数据: {item.get('instId')} - Bid: {bids[0][0]}, Ask: {asks[0][0]}")
                        break
                else:
                    print(f"  消息 {i+1}: {data}")
                
                await asyncio.sleep(1)
            
            print("✅ OKX WebSocket数据接收正常")
            return True
            
    except Exception as e:
        print(f"❌ OKX WebSocket连接失败: {e}")
        return False

async def test_bybit_connection():
    """测试Bybit WebSocket连接"""
    print("\n测试Bybit WebSocket连接...")

    try:
        ws_url = "wss://stream.bybit.com/v5/public/linear"

        async with websockets.connect(ws_url, ping_interval=20, ping_timeout=10) as websocket:
            print("✅ Bybit WebSocket连接成功")

            # 订阅BTCUSDT的orderbook
            subscribe_msg = {
                "op": "subscribe",
                "args": ["orderbook.1.BTCUSDT"]
            }

            await websocket.send(json.dumps(subscribe_msg))

            # 接收几条消息
            for i in range(5):
                message = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                data = json.loads(message)

                if 'data' in data and 'topic' in data:
                    topic = data['topic']
                    if 'orderbook.1.' in topic:
                        symbol = topic.replace('orderbook.1.', '')
                        order_data = data['data']
                        bids = order_data.get('b', [])
                        asks = order_data.get('a', [])

                        if bids and asks:
                            print(f"  数据: {symbol} - Bid: {bids[0][0]}, Ask: {asks[0][0]}")
                            break
                else:
                    print(f"  消息 {i+1}: {data}")

                await asyncio.sleep(1)

            print("✅ Bybit WebSocket数据接收正常")
            return True

    except Exception as e:
        print(f"❌ Bybit WebSocket连接失败: {e}")
        return False

async def test_bitget_connection():
    """测试Bitget WebSocket连接"""
    print("\n测试Bitget WebSocket连接...")

    try:
        ws_url = "wss://ws.bitget.com/mix/v1/stream"

        async with websockets.connect(ws_url, ping_interval=20, ping_timeout=10) as websocket:
            print("✅ Bitget WebSocket连接成功")

            # 订阅BTCUSDT的orderbook
            subscribe_msg = {
                "op": "subscribe",
                "args": [{
                    "instType": "mc",
                    "channel": "books5",
                    "instId": "BTCUSDT"
                }]
            }

            await websocket.send(json.dumps(subscribe_msg))

            # 接收几条消息
            for i in range(5):
                message = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                data = json.loads(message)

                if 'data' in data and data['data']:
                    for item in data['data']:
                        symbol = item.get('instId', '')
                        bids = item.get('bids', [])
                        asks = item.get('asks', [])

                        if bids and asks and symbol:
                            print(f"  数据: {symbol} - Bid: {bids[0][0]}, Ask: {asks[0][0]}")
                            break
                    break
                else:
                    print(f"  消息 {i+1}: {data}")

                await asyncio.sleep(1)

            print("✅ Bitget WebSocket数据接收正常")
            return True

    except Exception as e:
        print(f"❌ Bitget WebSocket连接失败: {e}")
        return False

async def test_all_exchanges():
    """测试所有交易所连接"""
    results = {}

    # 测试币安
    results['binance'] = await test_binance_connection()

    # 测试OKX
    results['okx'] = await test_okx_connection()

    # 测试Bybit
    results['bybit'] = await test_bybit_connection()

    # 测试Bitget
    results['bitget'] = await test_bitget_connection()

    return results

async def main():
    """主测试函数"""
    print("="*80)
    print("                    全面WebSocket连接测试")
    print("="*80)

    # 测试所有交易所
    results = await test_all_exchanges()

    print("\n" + "="*80)
    print("                        测试结果汇总")
    print("="*80)

    exchange_names = {
        'binance': 'Binance (币安)',
        'okx': 'OKX',
        'bybit': 'Bybit',
        'bitget': 'Bitget'
    }

    success_count = 0
    for exchange, success in results.items():
        status = '✅ 正常' if success else '❌ 失败'
        print(f"{exchange_names[exchange]:<15}: {status}")
        if success:
            success_count += 1

    print(f"\n成功连接: {success_count}/{len(results)} 个交易所")

    if success_count == len(results):
        print("\n🎉 所有WebSocket连接测试通过！")
        print("现在可以运行完整的WebSocket监控功能了。")
    elif success_count > 0:
        print(f"\n⚠️  {len(results) - success_count} 个交易所连接失败")
        print("可以使用成功连接的交易所进行监控")

        # 显示可用的交易所
        available = [ex for ex, ok in results.items() if ok]
        print(f"可用交易所: {', '.join(available)}")
        print(f"\n运行命令示例:")
        print(f"python 期货交易所交易对分析工具.py --exchanges {' '.join(available)} --websocket")
    else:
        print("\n❌ 所有WebSocket连接都失败了")
        print("请检查网络连接、防火墙设置或地区限制")

if __name__ == "__main__":
    asyncio.run(main())
