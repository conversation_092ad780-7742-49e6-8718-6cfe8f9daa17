#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WebSocket连接测试
"""

import asyncio
import websockets
import json
import time

async def test_binance_connection():
    """测试币安WebSocket连接"""
    print("测试币安WebSocket连接...")
    
    try:
        # 测试单个交易对的bookTicker
        ws_url = "wss://fstream.binance.com/ws/btcusdt@bookTicker"
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ 币安WebSocket连接成功")
            
            # 接收几条消息
            for i in range(3):
                message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                data = json.loads(message)
                
                print(f"  消息 {i+1}: {data.get('s')} - Bid: {data.get('b')}, Ask: {data.get('a')}")
                
                if i < 2:  # 不是最后一条消息时等待
                    await asyncio.sleep(1)
            
            print("✅ 币安WebSocket数据接收正常")
            return True
            
    except Exception as e:
        print(f"❌ 币安WebSocket连接失败: {e}")
        return False

async def test_okx_connection():
    """测试OKX WebSocket连接"""
    print("\n测试OKX WebSocket连接...")
    
    try:
        ws_url = "wss://ws.okx.com:8443/ws/v5/public"
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ OKX WebSocket连接成功")
            
            # 订阅BTC-USDT-SWAP的orderbook
            subscribe_msg = {
                "op": "subscribe",
                "args": [{
                    "channel": "books5",
                    "instId": "BTC-USDT-SWAP"
                }]
            }
            
            await websocket.send(json.dumps(subscribe_msg))
            
            # 接收几条消息
            for i in range(5):  # OKX可能先发送确认消息
                message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                data = json.loads(message)
                
                if 'data' in data and data['data']:
                    item = data['data'][0]
                    bids = item.get('bids', [])
                    asks = item.get('asks', [])
                    
                    if bids and asks:
                        print(f"  数据: {item.get('instId')} - Bid: {bids[0][0]}, Ask: {asks[0][0]}")
                        break
                else:
                    print(f"  消息 {i+1}: {data}")
                
                await asyncio.sleep(1)
            
            print("✅ OKX WebSocket数据接收正常")
            return True
            
    except Exception as e:
        print(f"❌ OKX WebSocket连接失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("="*60)
    print("           WebSocket连接测试")
    print("="*60)
    
    # 测试币安
    binance_ok = await test_binance_connection()
    
    # 测试OKX
    okx_ok = await test_okx_connection()
    
    print("\n" + "="*60)
    print("           测试结果")
    print("="*60)
    print(f"币安WebSocket: {'✅ 正常' if binance_ok else '❌ 失败'}")
    print(f"OKX WebSocket:  {'✅ 正常' if okx_ok else '❌ 失败'}")
    
    if binance_ok and okx_ok:
        print("\n🎉 所有WebSocket连接测试通过！")
        print("现在可以运行完整的WebSocket监控功能了。")
    else:
        print("\n⚠️  部分WebSocket连接测试失败")
        print("请检查网络连接或防火墙设置")

if __name__ == "__main__":
    asyncio.run(main())
