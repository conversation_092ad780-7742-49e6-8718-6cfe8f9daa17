#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的WebSocket连接测试 - 尝试多种连接方式
"""

import asyncio
import websockets
import json
import time
import ssl

async def test_binance_detailed():
    """详细测试币安WebSocket连接"""
    print("="*60)
    print("详细测试币安WebSocket连接")
    print("="*60)
    
    # 尝试多种URL和配置
    test_configs = [
        {
            'name': '币安期货 - 标准URL',
            'url': 'wss://fstream.binance.com/ws/btcusdt@bookTicker',
            'ssl_context': None
        },
        {
            'name': '币安期货 - 备用端口',
            'url': 'wss://fstream.binance.com:443/ws/btcusdt@bookTicker',
            'ssl_context': None
        },
        {
            'name': '币安现货 - 测试连通性',
            'url': 'wss://stream.binance.com:9443/ws/btcusdt@ticker',
            'ssl_context': None
        },
        {
            'name': '币安期货 - 忽略SSL',
            'url': 'wss://fstream.binance.com/ws/btcusdt@bookTicker',
            'ssl_context': ssl._create_unverified_context()
        }
    ]
    
    for config in test_configs:
        print(f"\n尝试: {config['name']}")
        print(f"URL: {config['url']}")
        
        try:
            connect_kwargs = {
                'ping_interval': 20,
                'ping_timeout': 10,
                'close_timeout': 10
            }
            
            if config['ssl_context']:
                connect_kwargs['ssl'] = config['ssl_context']
            
            async with websockets.connect(config['url'], **connect_kwargs) as websocket:
                print("✅ 连接成功！")
                
                # 尝试接收数据
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    data = json.loads(message)
                    print(f"✅ 数据接收成功: {data.get('s', 'Unknown')} - Bid: {data.get('b', 'N/A')}")
                    return True
                except asyncio.TimeoutError:
                    print("⚠️  连接成功但未收到数据")
                    return True
                    
        except Exception as e:
            print(f"❌ 连接失败: {str(e)[:100]}")
    
    return False

async def test_bybit_detailed():
    """详细测试Bybit WebSocket连接"""
    print("\n" + "="*60)
    print("详细测试Bybit WebSocket连接")
    print("="*60)
    
    test_configs = [
        {
            'name': 'Bybit V5 线性合约',
            'url': 'wss://stream.bybit.com/v5/public/linear',
            'subscribe': {
                "op": "subscribe",
                "args": ["orderbook.1.BTCUSDT"]
            }
        },
        {
            'name': 'Bybit V5 现货',
            'url': 'wss://stream.bybit.com/v5/public/spot',
            'subscribe': {
                "op": "subscribe", 
                "args": ["orderbook.1.BTCUSDT"]
            }
        },
        {
            'name': 'Bybit 测试网',
            'url': 'wss://stream-testnet.bybit.com/v5/public/linear',
            'subscribe': {
                "op": "subscribe",
                "args": ["orderbook.1.BTCUSDT"]
            }
        }
    ]
    
    for config in test_configs:
        print(f"\n尝试: {config['name']}")
        print(f"URL: {config['url']}")
        
        try:
            async with websockets.connect(
                config['url'], 
                ping_interval=20, 
                ping_timeout=10,
                close_timeout=10
            ) as websocket:
                print("✅ 连接成功！")
                
                # 发送订阅消息
                await websocket.send(json.dumps(config['subscribe']))
                print(f"📤 发送订阅: {config['subscribe']}")
                
                # 尝试接收数据
                for i in range(3):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        data = json.loads(message)
                        print(f"📥 消息 {i+1}: {str(data)[:100]}...")
                        
                        if 'data' in data and 'topic' in data:
                            print("✅ 数据接收成功！")
                            return True
                    except asyncio.TimeoutError:
                        print(f"⏰ 消息 {i+1} 超时")
                        
        except Exception as e:
            print(f"❌ 连接失败: {str(e)[:100]}")
    
    return False

async def test_bitget_detailed():
    """详细测试Bitget WebSocket连接"""
    print("\n" + "="*60)
    print("详细测试Bitget WebSocket连接")
    print("="*60)
    
    test_configs = [
        {
            'name': 'Bitget 合约',
            'url': 'wss://ws.bitget.com/mix/v1/stream',
            'subscribe': {
                "op": "subscribe",
                "args": [{
                    "instType": "mc",
                    "channel": "books5",
                    "instId": "BTCUSDT"
                }]
            }
        },
        {
            'name': 'Bitget 现货',
            'url': 'wss://ws.bitget.com/spot/v1/stream',
            'subscribe': {
                "op": "subscribe",
                "args": [{
                    "instType": "sp",
                    "channel": "books5",
                    "instId": "BTCUSDT"
                }]
            }
        }
    ]
    
    for config in test_configs:
        print(f"\n尝试: {config['name']}")
        print(f"URL: {config['url']}")
        
        try:
            async with websockets.connect(
                config['url'], 
                ping_interval=20, 
                ping_timeout=10,
                close_timeout=10
            ) as websocket:
                print("✅ 连接成功！")
                
                # 发送订阅消息
                await websocket.send(json.dumps(config['subscribe']))
                print(f"📤 发送订阅: {config['subscribe']}")
                
                # 尝试接收数据
                for i in range(3):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        data = json.loads(message)
                        print(f"📥 消息 {i+1}: {str(data)[:100]}...")
                        
                        if 'data' in data and data.get('data'):
                            print("✅ 数据接收成功！")
                            return True
                    except asyncio.TimeoutError:
                        print(f"⏰ 消息 {i+1} 超时")
                        
        except Exception as e:
            print(f"❌ 连接失败: {str(e)[:100]}")
    
    return False

async def test_network_connectivity():
    """测试网络连通性"""
    print("\n" + "="*60)
    print("测试网络连通性")
    print("="*60)
    
    # 测试一些公共WebSocket服务
    test_urls = [
        'wss://echo.websocket.org',
        'wss://ws.postman-echo.com/raw'
    ]
    
    for url in test_urls:
        print(f"\n测试: {url}")
        try:
            async with websockets.connect(url, ping_interval=20, ping_timeout=10) as websocket:
                print("✅ 连接成功")
                await websocket.send("test")
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"✅ 收到响应: {response}")
        except Exception as e:
            print(f"❌ 连接失败: {e}")

async def main():
    """主测试函数"""
    print("="*80)
    print("                    详细WebSocket连接诊断")
    print("="*80)
    
    # 先测试网络连通性
    await test_network_connectivity()
    
    # 测试各个交易所
    results = {}
    
    print("\n" + "="*80)
    print("                    交易所WebSocket测试")
    print("="*80)
    
    results['binance'] = await test_binance_detailed()
    results['bybit'] = await test_bybit_detailed()
    results['bitget'] = await test_bitget_detailed()
    
    # 汇总结果
    print("\n" + "="*80)
    print("                        最终测试结果")
    print("="*80)
    
    exchange_names = {
        'binance': 'Binance (币安)',
        'bybit': 'Bybit',
        'bitget': 'Bitget'
    }
    
    success_count = 0
    for exchange, success in results.items():
        status = '✅ 可用' if success else '❌ 不可用'
        print(f"{exchange_names[exchange]:<15}: {status}")
        if success:
            success_count += 1
    
    print(f"\n可用交易所: {success_count}/{len(results)} 个")
    
    if success_count > 0:
        available = [ex for ex, ok in results.items() if ok]
        print(f"建议使用: {', '.join(available)}")
    else:
        print("建议检查网络连接或使用VPN")

if __name__ == "__main__":
    asyncio.run(main())
