#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理连接测试脚本
"""

import asyncio
import websockets
import json
import time
import logging
from urllib.parse import urlparse

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_proxy_connection(proxy_url, target_url="wss://echo.websocket.org"):
    """测试代理连接"""
    print(f"测试代理: {proxy_url}")
    print(f"目标URL: {target_url}")
    
    try:
        # 解析代理URL
        parsed_proxy = urlparse(proxy_url)
        
        connect_kwargs = {
            'ping_interval': 20,
            'ping_timeout': 10,
            'close_timeout': 10
        }
        
        # 根据代理类型配置连接参数
        if parsed_proxy.scheme in ['http', 'https']:
            # HTTP代理
            try:
                import aiohttp
                connect_kwargs['proxy'] = proxy_url
                print("✅ HTTP代理配置完成")
            except ImportError:
                print("❌ 需要安装aiohttp: pip install aiohttp")
                return False
                
        elif parsed_proxy.scheme in ['socks4', 'socks5']:
            # SOCKS代理
            try:
                import python_socks
                print("⚠️  SOCKS代理需要特殊处理，当前测试可能不准确")
            except ImportError:
                print("❌ 需要安装python-socks: pip install python-socks")
                return False
        
        # 尝试连接
        async with websockets.connect(target_url, **connect_kwargs) as websocket:
            print("✅ 代理连接成功")
            
            # 发送测试消息
            test_message = "proxy_test_message"
            await websocket.send(test_message)
            
            # 接收响应
            response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
            print(f"✅ 收到响应: {response}")
            
            return True
            
    except Exception as e:
        print(f"❌ 代理连接失败: {e}")
        return False

async def test_exchange_with_proxy(proxy_url, exchange_configs):
    """使用代理测试交易所连接"""
    print(f"\n使用代理测试交易所连接: {proxy_url}")
    print("="*60)
    
    results = {}
    
    for exchange, config in exchange_configs.items():
        print(f"\n测试 {exchange.upper()}...")
        
        try:
            # 解析代理URL
            parsed_proxy = urlparse(proxy_url)
            
            connect_kwargs = {
                'ping_interval': 20,
                'ping_timeout': 10,
                'close_timeout': 10
            }
            
            # 配置代理
            if parsed_proxy.scheme in ['http', 'https']:
                try:
                    import aiohttp
                    connect_kwargs['proxy'] = proxy_url
                except ImportError:
                    print("❌ 需要安装aiohttp")
                    results[exchange] = False
                    continue
            
            # 尝试连接
            async with websockets.connect(config['url'], **connect_kwargs) as websocket:
                print(f"✅ {exchange} 连接成功")
                
                # 发送订阅消息（如果有）
                if 'subscribe' in config:
                    await websocket.send(json.dumps(config['subscribe']))
                    print(f"📤 发送订阅消息")
                
                # 等待响应
                for i in range(3):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        data = json.loads(message)
                        print(f"📥 收到消息 {i+1}: {str(data)[:100]}...")
                        
                        # 检查是否收到有效数据
                        if exchange == 'okx' and 'data' in data and data.get('data'):
                            print(f"✅ {exchange} 数据接收正常")
                            results[exchange] = True
                            break
                        elif exchange == 'binance' and ('s' in data or ('stream' in data and 'data' in data)):
                            print(f"✅ {exchange} 数据接收正常")
                            results[exchange] = True
                            break
                            
                    except asyncio.TimeoutError:
                        print(f"⏰ 消息 {i+1} 超时")
                
                if exchange not in results:
                    results[exchange] = True  # 连接成功但可能没有数据
                    
        except Exception as e:
            print(f"❌ {exchange} 连接失败: {e}")
            results[exchange] = False
    
    return results

def main():
    """主函数"""
    print("="*80)
    print("                    WebSocket代理连接测试")
    print("="*80)
    
    # 常见代理配置示例
    proxy_examples = [
        "http://127.0.0.1:7890",    # Clash HTTP代理
        "http://127.0.0.1:8080",    # 常见HTTP代理
        "socks5://127.0.0.1:1080",  # SOCKS5代理
        "socks5://127.0.0.1:7891",  # Clash SOCKS5代理
    ]
    
    print("常见代理配置示例:")
    for i, proxy in enumerate(proxy_examples, 1):
        print(f"  {i}. {proxy}")
    
    # 让用户输入代理URL
    proxy_url = input("\n请输入代理URL (直接回车跳过代理测试): ").strip()
    
    if not proxy_url:
        print("跳过代理测试")
        return
    
    # 交易所配置
    exchange_configs = {
        'binance': {
            'url': 'wss://fstream.binance.com/ws/btcusdt@bookTicker'
        },
        'okx': {
            'url': 'wss://ws.okx.com:8443/ws/v5/public',
            'subscribe': {
                "op": "subscribe",
                "args": [{
                    "channel": "books5",
                    "instId": "BTC-USDT-SWAP"
                }]
            }
        },
        'bybit': {
            'url': 'wss://stream.bybit.com/v5/public/linear',
            'subscribe': {
                "op": "subscribe",
                "args": ["orderbook.1.BTCUSDT"]
            }
        }
    }
    
    async def run_tests():
        # 1. 基础代理连接测试
        print("\n1. 基础代理连接测试")
        print("-" * 40)
        basic_test = await test_proxy_connection(proxy_url)
        
        if not basic_test:
            print("\n❌ 基础代理测试失败，跳过交易所测试")
            return
        
        # 2. 交易所代理连接测试
        print("\n2. 交易所代理连接测试")
        print("-" * 40)
        exchange_results = await test_exchange_with_proxy(proxy_url, exchange_configs)
        
        # 3. 结果汇总
        print("\n" + "="*60)
        print("                    测试结果汇总")
        print("="*60)
        
        success_count = sum(1 for success in exchange_results.values() if success)
        total_count = len(exchange_results)
        
        for exchange, success in exchange_results.items():
            status = "✅ 可用" if success else "❌ 不可用"
            print(f"{exchange.upper():<10}: {status}")
        
        print(f"\n成功连接: {success_count}/{total_count} 个交易所")
        
        if success_count > 0:
            available = [ex for ex, ok in exchange_results.items() if ok]
            print(f"\n🎉 代理配置有效！")
            print(f"可用交易所: {', '.join(available)}")
            print(f"\n使用代理运行完整程序:")
            print(f"python 期货交易所交易对分析工具.py --proxy {proxy_url} --websocket --exchanges {' '.join(available)}")
        else:
            print(f"\n❌ 代理无法访问交易所WebSocket")
            print("请检查代理配置或尝试其他代理")
    
    # 运行测试
    try:
        asyncio.run(run_tests())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
