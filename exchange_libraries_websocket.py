#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用交易所专门库的WebSocket连接实现
"""

import time
import logging
import threading
from typing import Dict, List, Optional, Callable
from concurrent.futures import ThreadPoolExecutor
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExchangeLibraryWebSocket:
    """使用交易所专门库的WebSocket管理器"""
    
    def __init__(self):
        self.price_data = {}  # 存储实时价格数据
        self.ws_running = False
        self.ws_connections = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.callbacks = {}  # 数据回调函数
        
    def set_data_callback(self, callback: Callable):
        """设置数据回调函数"""
        self.callbacks['data'] = callback
    
    def _on_price_update(self, exchange: str, symbol: str, bid: float, ask: float):
        """价格更新回调"""
        if exchange not in self.price_data:
            self.price_data[exchange] = {}
        
        self.price_data[exchange][symbol] = {
            'bid': bid,
            'ask': ask,
            'timestamp': time.time()
        }
        
        # 调用用户回调
        if 'data' in self.callbacks:
            self.callbacks['data'](exchange, symbol, bid, ask)
    
    def start_binance_websocket(self, symbols: List[str]):
        """启动币安WebSocket连接"""
        try:
            from binance import ThreadedWebsocketManager

            def handle_socket_message(msg):
                try:
                    if isinstance(msg, dict) and msg.get('e') == 'bookTicker':
                        symbol = msg['s']
                        bid = float(msg['b'])
                        ask = float(msg['a'])
                        self._on_price_update('binance', symbol, bid, ask)
                except Exception as e:
                    logger.error(f"Binance message handling error: {e}")

            # 创建WebSocket管理器
            twm = ThreadedWebsocketManager()
            twm.start()

            # 订阅多个交易对的bookTicker
            streams = [f"{symbol.lower()}@bookTicker" for symbol in symbols]
            twm.start_multiplex_socket(callback=handle_socket_message, streams=streams)

            self.ws_connections['binance'] = twm
            logger.info(f"✅ Binance WebSocket started for {len(symbols)} symbols")
            return True

        except ImportError as e:
            logger.error(f"❌ python-binance library issue: {e}")
            logger.info("Try: pip install python-binance --upgrade")
            return False
        except Exception as e:
            logger.error(f"❌ Binance WebSocket connection failed: {e}")
            return False
    
    def start_okx_websocket(self, symbols: List[str]):
        """启动OKX WebSocket连接"""
        try:
            import okx.PublicData as PublicData
            import okx.MarketData as MarketData
            
            # 这里使用简化的实现，实际可能需要更复杂的配置
            logger.info("⚠️  OKX library WebSocket implementation needs custom setup")
            logger.info("Using fallback to direct websocket connection")
            return False
            
        except ImportError:
            logger.error("❌ okx-sdk library not installed: pip install okx-sdk")
            return False
        except Exception as e:
            logger.error(f"❌ OKX WebSocket connection failed: {e}")
            return False
    
    def start_bybit_websocket(self, symbols: List[str]):
        """启动Bybit WebSocket连接"""
        try:
            from pybit.unified_trading import WebSocket
            
            def handle_message(message):
                try:
                    if 'data' in message and 'topic' in message:
                        topic = message['topic']
                        if 'orderbook.1.' in topic:
                            symbol = topic.replace('orderbook.1.', '')
                            data = message['data']
                            
                            bids = data.get('b', [])
                            asks = data.get('a', [])
                            
                            if bids and asks:
                                bid = float(bids[0][0])
                                ask = float(asks[0][0])
                                self._on_price_update('bybit', symbol, bid, ask)
                                
                except Exception as e:
                    logger.error(f"Bybit message handling error: {e}")
            
            # 创建WebSocket连接
            ws = WebSocket(
                testnet=False,
                channel_type="linear"
            )
            
            # 订阅orderbook数据
            for symbol in symbols:
                ws.orderbook_stream(
                    depth=1,
                    symbol=symbol,
                    callback=handle_message
                )
            
            self.ws_connections['bybit'] = ws
            logger.info(f"✅ Bybit WebSocket started for {len(symbols)} symbols")
            return True
            
        except ImportError:
            logger.error("❌ pybit library not installed: pip install pybit")
            return False
        except Exception as e:
            logger.error(f"❌ Bybit WebSocket connection failed: {e}")
            return False
    
    def start_ccxt_websocket(self, exchange_name: str, symbols: List[str]):
        """使用CCXT启动WebSocket连接"""
        try:
            import ccxt.pro as ccxtpro
            
            async def watch_orderbook():
                exchange_class = getattr(ccxtpro, exchange_name)
                exchange = exchange_class()
                
                try:
                    while self.ws_running:
                        for symbol in symbols:
                            try:
                                orderbook = await exchange.watch_order_book(symbol)
                                
                                if orderbook['bids'] and orderbook['asks']:
                                    bid = orderbook['bids'][0][0]
                                    ask = orderbook['asks'][0][0]
                                    self._on_price_update(exchange_name, symbol, bid, ask)
                                    
                            except Exception as e:
                                logger.debug(f"CCXT {exchange_name} {symbol} error: {e}")
                                
                        await asyncio.sleep(0.1)
                        
                finally:
                    await exchange.close()
            
            # 在线程中运行异步函数
            import asyncio
            
            def run_ccxt_ws():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(watch_orderbook())
                loop.close()
            
            thread = threading.Thread(target=run_ccxt_ws)
            thread.daemon = True
            thread.start()
            
            self.ws_connections[f'ccxt_{exchange_name}'] = thread
            logger.info(f"✅ CCXT {exchange_name} WebSocket started for {len(symbols)} symbols")
            return True
            
        except ImportError:
            logger.error("❌ ccxt.pro library not installed: pip install ccxt[pro]")
            return False
        except Exception as e:
            logger.error(f"❌ CCXT {exchange_name} WebSocket connection failed: {e}")
            return False
    
    def start_all_websockets(self, exchanges_symbols: Dict[str, List[str]]):
        """启动所有交易所的WebSocket连接"""
        self.ws_running = True
        results = {}
        
        for exchange, symbols in exchanges_symbols.items():
            logger.info(f"Starting {exchange} WebSocket...")
            
            if exchange == 'binance':
                results[exchange] = self.start_binance_websocket(symbols)
            elif exchange == 'okx':
                # 尝试CCXT方式
                results[exchange] = self.start_ccxt_websocket('okx', symbols)
            elif exchange == 'bybit':
                results[exchange] = self.start_bybit_websocket(symbols)
            elif exchange == 'bitget':
                # 尝试CCXT方式
                results[exchange] = self.start_ccxt_websocket('bitget', symbols)
            else:
                logger.warning(f"Unknown exchange: {exchange}")
                results[exchange] = False
        
        return results
    
    def stop_all_websockets(self):
        """停止所有WebSocket连接"""
        logger.info("Stopping all WebSocket connections...")
        self.ws_running = False
        
        for name, connection in self.ws_connections.items():
            try:
                if hasattr(connection, 'stop'):
                    connection.stop()
                elif hasattr(connection, 'close'):
                    connection.close()
                logger.info(f"✅ Stopped {name} WebSocket")
            except Exception as e:
                logger.warning(f"Error stopping {name} WebSocket: {e}")
        
        self.ws_connections.clear()
        logger.info("All WebSocket connections stopped")
    
    def get_price_data(self) -> Dict:
        """获取当前价格数据"""
        return self.price_data.copy()
    
    def get_active_connections(self) -> List[str]:
        """获取活跃连接列表"""
        active = []
        current_time = time.time()
        
        for exchange, symbols_data in self.price_data.items():
            for symbol, price_info in symbols_data.items():
                if current_time - price_info['timestamp'] < 60:  # 1分钟内有数据更新
                    if exchange not in active:
                        active.append(exchange)
                    break
        
        return active

def test_exchange_libraries():
    """测试交易所库连接"""
    print("="*80)
    print("                    交易所专门库WebSocket测试")
    print("="*80)
    
    # 创建WebSocket管理器
    ws_manager = ExchangeLibraryWebSocket()
    
    # 设置数据回调
    def on_data_update(exchange, symbol, bid, ask):
        print(f"📈 {exchange.upper()} {symbol}: Bid={bid:.6f}, Ask={ask:.6f}")
    
    ws_manager.set_data_callback(on_data_update)
    
    # 测试配置
    test_symbols = {
        'binance': ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'],
        'bybit': ['BTCUSDT', 'ETHUSDT'],
        'okx': ['BTC-USDT-SWAP', 'ETH-USDT-SWAP'],
    }
    
    try:
        # 启动WebSocket连接
        results = ws_manager.start_all_websockets(test_symbols)
        
        print("\n连接结果:")
        for exchange, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {exchange}: {status}")
        
        # 监控30秒
        print(f"\n开始监控30秒...")
        start_time = time.time()
        
        while time.time() - start_time < 30:
            time.sleep(5)
            
            # 显示活跃连接
            active = ws_manager.get_active_connections()
            if active:
                print(f"活跃连接: {', '.join(active)}")
            else:
                print("暂无活跃连接")
        
        # 显示最终统计
        print(f"\n最终统计:")
        price_data = ws_manager.get_price_data()
        for exchange, symbols_data in price_data.items():
            print(f"  {exchange}: {len(symbols_data)} 个交易对有数据")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    finally:
        # 停止所有连接
        ws_manager.stop_all_websockets()
        print("测试完成")

if __name__ == "__main__":
    test_exchange_libraries()
