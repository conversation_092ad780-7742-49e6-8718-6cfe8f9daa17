#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的代理连接测试
"""

import asyncio
import websockets
import json
import logging
from urllib.parse import urlparse

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_websocket_with_proxy(url, proxy_url=None):
    """使用代理测试WebSocket连接"""
    try:
        connect_kwargs = {
            'ping_interval': 20,
            'ping_timeout': 10,
            'close_timeout': 10
        }
        
        # 如果有代理配置，尝试添加代理参数
        if proxy_url:
            parsed_proxy = urlparse(proxy_url)
            
            if parsed_proxy.scheme in ['http', 'https']:
                # 对于HTTP代理，websockets库可能不直接支持
                # 这里我们记录配置但可能需要其他方法
                logger.info(f"尝试使用HTTP代理: {proxy_url}")
                # 注意：websockets库对HTTP代理的支持有限
                # 实际使用中可能需要使用其他库或方法
                
        # 尝试连接
        async with websockets.connect(url, **connect_kwargs) as websocket:
            logger.info(f"✅ 连接成功: {url}")
            
            # 发送测试消息
            if "echo.websocket.org" in url:
                await websocket.send("test")
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"✅ 收到响应: {response}")
            else:
                # 等待一下看是否有数据
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    logger.info(f"✅ 收到数据: {str(data)[:100]}...")
                except asyncio.TimeoutError:
                    logger.info("⏰ 未收到数据，但连接正常")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 连接失败: {e}")
        return False

def test_system_proxy():
    """测试系统代理设置"""
    import os
    
    print("检查系统代理环境变量:")
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"  {var}: {value}")
        else:
            print(f"  {var}: 未设置")

async def main():
    """主函数"""
    print("="*60)
    print("           简化代理连接测试")
    print("="*60)
    
    # 检查系统代理
    test_system_proxy()
    
    print("\n" + "="*60)
    print("           WebSocket连接测试")
    print("="*60)
    
    # 测试URL列表
    test_urls = [
        ("基础连通性", "wss://echo.websocket.org"),
        ("币安期货", "wss://fstream.binance.com/ws/btcusdt@bookTicker"),
        ("OKX", "wss://ws.okx.com:8443/ws/v5/public"),
        ("Bybit", "wss://stream.bybit.com/v5/public/linear"),
    ]
    
    results = {}
    
    for name, url in test_urls:
        print(f"\n测试 {name}...")
        result = await test_websocket_with_proxy(url)
        results[name] = result
    
    # 显示结果
    print("\n" + "="*60)
    print("           测试结果汇总")
    print("="*60)
    
    for name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{name:<15}: {status}")
    
    # 给出建议
    print("\n" + "="*60)
    print("           建议和说明")
    print("="*60)
    
    if results.get("基础连通性", False):
        print("✅ 基础WebSocket连接正常")
    else:
        print("❌ 基础WebSocket连接异常，请检查网络")
    
    exchange_success = sum(1 for name, success in results.items() 
                          if name != "基础连通性" and success)
    
    if exchange_success == 0:
        print("\n💡 所有交易所连接失败，建议:")
        print("   1. 使用代理软件（如Clash、V2Ray）")
        print("   2. 设置系统代理环境变量")
        print("   3. 使用程序的--proxy参数")
        print("\n   示例:")
        print("   export HTTP_PROXY=http://127.0.0.1:7890")
        print("   export HTTPS_PROXY=http://127.0.0.1:7890")
        print("   python 期货交易所交易对分析工具.py --proxy http://127.0.0.1:7890 --websocket")
    
    elif exchange_success < 3:
        print(f"\n⚠️  部分交易所连接成功 ({exchange_success}/3)")
        print("   可以使用成功的交易所进行监控")
        
        available = [name for name, success in results.items() 
                    if name != "基础连通性" and success]
        print(f"   可用: {', '.join(available)}")
    
    else:
        print(f"\n🎉 大部分交易所连接成功！({exchange_success}/3)")
        print("   可以直接使用WebSocket功能")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
