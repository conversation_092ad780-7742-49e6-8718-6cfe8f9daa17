#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket功能测试脚本
"""

import sys
import time
import logging
from 期货交易所交易对分析工具 import FuturesAnalyzer

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_websocket_basic():
    """基础WebSocket功能测试"""
    print("="*80)
    print("                    WEBSOCKET BASIC FUNCTIONALITY TEST")
    print("="*80)
    
    # 创建分析器
    analyzer = FuturesAnalyzer()
    
    try:
        # 获取交易对数据
        print("1. Fetching trading pairs data...")
        analyzer.fetch_all_data(['binance'])  # 只测试币安
        
        # 启动WebSocket连接（只订阅少量交易对进行测试）
        print("2. Starting WebSocket connections...")
        analyzer.start_websocket_streams(['binance'], max_symbols_per_exchange=5)
        
        # 监控30秒
        print("3. Monitoring for 30 seconds...")
        monitor_start = time.time()
        last_check = 0
        
        while time.time() - monitor_start < 30:
            current_time = time.time()
            
            # 每5秒检查一次数据
            if current_time - last_check >= 5:
                prices = analyzer.get_current_prices('binance')
                print(f"\nReceived price data for {len(prices)} symbols:")
                
                for symbol, price_info in list(prices.items())[:3]:  # 只显示前3个
                    print(f"  {symbol}: Bid={price_info['bid']:.6f}, Ask={price_info['ask']:.6f}")
                
                last_check = current_time
            
            time.sleep(1)
        
        # 停止WebSocket
        print("4. Stopping WebSocket connections...")
        analyzer.stop_websocket_streams()
        
        # 显示最终统计
        print("5. Final statistics:")
        analyzer.print_price_summary()
        
        print("\n✅ WebSocket test completed successfully!")
        
    except Exception as e:
        logger.error(f"WebSocket test failed: {e}")
        return False
    
    return True

def test_arbitrage_detection():
    """套利检测功能测试"""
    print("\n" + "="*80)
    print("                    ARBITRAGE DETECTION TEST")
    print("="*80)
    
    # 创建分析器
    analyzer = FuturesAnalyzer()
    
    try:
        # 获取交易对数据
        print("1. Fetching trading pairs data...")
        analyzer.fetch_all_data(['binance', 'okx'])  # 测试两个交易所
        
        # 启动WebSocket连接
        print("2. Starting WebSocket connections...")
        analyzer.start_websocket_streams(['binance', 'okx'], max_symbols_per_exchange=10)
        
        # 监控60秒寻找套利机会
        print("3. Monitoring for arbitrage opportunities (60 seconds)...")
        monitor_start = time.time()
        last_check = 0
        
        while time.time() - monitor_start < 60:
            current_time = time.time()
            
            # 每15秒检查一次套利机会
            if current_time - last_check >= 15:
                print(f"\n--- Checking arbitrage opportunities at {time.strftime('%H:%M:%S')} ---")
                analyzer.print_arbitrage_opportunities(min_spread_pct=0.05, max_results=5)
                last_check = current_time
            
            time.sleep(1)
        
        # 停止WebSocket
        print("4. Stopping WebSocket connections...")
        analyzer.stop_websocket_streams()
        
        print("\n✅ Arbitrage detection test completed!")
        
    except Exception as e:
        logger.error(f"Arbitrage test failed: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("Starting WebSocket functionality tests...\n")
    
    # 基础功能测试
    if not test_websocket_basic():
        print("❌ Basic WebSocket test failed!")
        sys.exit(1)
    
    # 套利检测测试
    if not test_arbitrage_detection():
        print("❌ Arbitrage detection test failed!")
        sys.exit(1)
    
    print("\n" + "="*80)
    print("                    ALL TESTS PASSED! ✅")
    print("="*80)

if __name__ == "__main__":
    main()
