#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于CCXT Pro的统一WebSocket实现
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Callable
import json
from concurrent.futures import ThreadPoolExecutor
import threading

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CCXTWebSocketManager:
    """基于CCXT Pro的WebSocket管理器"""
    
    def __init__(self):
        self.price_data = {}  # 存储实时价格数据
        self.exchanges = {}   # 存储交易所实例
        self.running = False
        self.tasks = {}       # 存储异步任务
        self.callbacks = {}   # 数据回调函数
        self.loop = None
        self.executor = ThreadPoolExecutor(max_workers=1)
        
    def set_data_callback(self, callback: Callable):
        """设置数据回调函数"""
        self.callbacks['data'] = callback
    
    def _on_price_update(self, exchange_name: str, symbol: str, bid: float, ask: float):
        """价格更新回调"""
        if exchange_name not in self.price_data:
            self.price_data[exchange_name] = {}
        
        self.price_data[exchange_name][symbol] = {
            'bid': bid,
            'ask': ask,
            'timestamp': time.time()
        }
        
        # 调用用户回调
        if 'data' in self.callbacks:
            self.callbacks['data'](exchange_name, symbol, bid, ask)
    
    async def _watch_orderbook_for_exchange(self, exchange_name: str, symbols: List[str]):
        """为单个交易所监控orderbook"""
        try:
            import ccxt.pro as ccxtpro
            
            # 创建交易所实例
            exchange_class = getattr(ccxtpro, exchange_name)
            exchange = exchange_class({
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchanges[exchange_name] = exchange
            logger.info(f"✅ {exchange_name.upper()} CCXT Pro instance created")
            
            # 监控所有符号的orderbook
            while self.running:
                try:
                    # 并发监控多个符号
                    tasks = []
                    for symbol in symbols:
                        task = asyncio.create_task(self._watch_single_orderbook(exchange, exchange_name, symbol))
                        tasks.append(task)
                    
                    # 等待所有任务完成或超时
                    await asyncio.wait(tasks, timeout=1.0, return_when=asyncio.FIRST_COMPLETED)
                    
                    # 取消未完成的任务
                    for task in tasks:
                        if not task.done():
                            task.cancel()
                    
                except Exception as e:
                    logger.error(f"{exchange_name} orderbook monitoring error: {e}")
                    await asyncio.sleep(1)
            
        except ImportError:
            logger.error("❌ CCXT Pro not installed: pip install ccxt[pro]")
        except Exception as e:
            logger.error(f"❌ {exchange_name} CCXT Pro connection failed: {e}")
        finally:
            # 关闭交易所连接
            if exchange_name in self.exchanges:
                try:
                    await self.exchanges[exchange_name].close()
                except:
                    pass
    
    async def _watch_single_orderbook(self, exchange, exchange_name: str, symbol: str):
        """监控单个交易对的orderbook"""
        try:
            orderbook = await exchange.watch_order_book(symbol, limit=5)
            
            if orderbook and 'bids' in orderbook and 'asks' in orderbook:
                bids = orderbook['bids']
                asks = orderbook['asks']
                
                if bids and asks and len(bids) > 0 and len(asks) > 0:
                    bid = float(bids[0][0])  # 最高买价
                    ask = float(asks[0][0])  # 最低卖价
                    
                    self._on_price_update(exchange_name, symbol, bid, ask)
                    
        except Exception as e:
            logger.debug(f"{exchange_name} {symbol} orderbook error: {e}")
    
    async def _watch_ticker_for_exchange(self, exchange_name: str, symbols: List[str]):
        """为单个交易所监控ticker（备选方案）"""
        try:
            import ccxt.pro as ccxtpro
            
            exchange_class = getattr(ccxtpro, exchange_name)
            exchange = exchange_class({
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            self.exchanges[f"{exchange_name}_ticker"] = exchange
            logger.info(f"✅ {exchange_name.upper()} ticker monitoring started")
            
            while self.running:
                try:
                    for symbol in symbols:
                        try:
                            ticker = await exchange.watch_ticker(symbol)
                            
                            if ticker and 'bid' in ticker and 'ask' in ticker:
                                bid = float(ticker['bid']) if ticker['bid'] else 0
                                ask = float(ticker['ask']) if ticker['ask'] else 0
                                
                                if bid > 0 and ask > 0:
                                    self._on_price_update(exchange_name, symbol, bid, ask)
                                    
                        except Exception as e:
                            logger.debug(f"{exchange_name} {symbol} ticker error: {e}")
                    
                    await asyncio.sleep(0.1)  # 短暂休息
                    
                except Exception as e:
                    logger.error(f"{exchange_name} ticker monitoring error: {e}")
                    await asyncio.sleep(1)
                    
        except Exception as e:
            logger.error(f"❌ {exchange_name} ticker monitoring failed: {e}")
        finally:
            # 关闭连接
            if f"{exchange_name}_ticker" in self.exchanges:
                try:
                    await self.exchanges[f"{exchange_name}_ticker"].close()
                except:
                    pass
    
    def start_websocket_streams(self, exchanges_symbols: Dict[str, List[str]], use_ticker_fallback: bool = True):
        """启动WebSocket数据流"""
        self.running = True
        
        def run_async_loop():
            """在单独线程中运行异步事件循环"""
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            try:
                # 创建所有监控任务
                tasks = []
                
                for exchange_name, symbols in exchanges_symbols.items():
                    logger.info(f"Starting CCXT Pro WebSocket for {exchange_name} with {len(symbols)} symbols")
                    
                    # 主要方案：监控orderbook
                    task = self.loop.create_task(
                        self._watch_orderbook_for_exchange(exchange_name, symbols)
                    )
                    tasks.append(task)
                    self.tasks[f"{exchange_name}_orderbook"] = task
                    
                    # 备选方案：监控ticker（如果启用）
                    if use_ticker_fallback:
                        ticker_task = self.loop.create_task(
                            self._watch_ticker_for_exchange(exchange_name, symbols)
                        )
                        tasks.append(ticker_task)
                        self.tasks[f"{exchange_name}_ticker"] = ticker_task
                
                # 运行事件循环
                self.loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
                
            except Exception as e:
                logger.error(f"Async loop error: {e}")
            finally:
                self.loop.close()
        
        # 在线程池中启动异步循环
        future = self.executor.submit(run_async_loop)
        logger.info("CCXT Pro WebSocket streams started")
        
        return future
    
    def stop_websocket_streams(self):
        """停止WebSocket数据流"""
        logger.info("Stopping CCXT Pro WebSocket streams...")
        self.running = False
        
        # 取消所有任务
        if self.loop and not self.loop.is_closed():
            for task_name, task in self.tasks.items():
                if not task.done():
                    task.cancel()
                    logger.info(f"Cancelled task: {task_name}")
        
        # 关闭所有交易所连接
        async def close_all_exchanges():
            for exchange_name, exchange in self.exchanges.items():
                try:
                    await exchange.close()
                    logger.info(f"Closed {exchange_name} connection")
                except Exception as e:
                    logger.warning(f"Error closing {exchange_name}: {e}")
        
        if self.loop and not self.loop.is_closed():
            try:
                self.loop.run_until_complete(close_all_exchanges())
            except:
                pass
        
        self.tasks.clear()
        self.exchanges.clear()
        logger.info("All CCXT Pro WebSocket streams stopped")
    
    def get_price_data(self) -> Dict:
        """获取当前价格数据"""
        return self.price_data.copy()
    
    def get_active_connections(self) -> List[str]:
        """获取活跃连接列表"""
        active = []
        current_time = time.time()
        
        for exchange, symbols_data in self.price_data.items():
            for symbol, price_info in symbols_data.items():
                if current_time - price_info['timestamp'] < 60:  # 1分钟内有数据更新
                    if exchange not in active:
                        active.append(exchange)
                    break
        
        return active
    
    def get_connection_status(self) -> Dict:
        """获取连接状态"""
        status = {}
        current_time = time.time()
        
        for exchange, symbols_data in self.price_data.items():
            active_symbols = 0
            total_symbols = len(symbols_data)
            latest_update = 0
            
            for symbol, price_info in symbols_data.items():
                if current_time - price_info['timestamp'] < 60:
                    active_symbols += 1
                latest_update = max(latest_update, price_info['timestamp'])
            
            status[exchange] = {
                'total_symbols': total_symbols,
                'active_symbols': active_symbols,
                'latest_update': latest_update,
                'status': 'Active' if active_symbols > 0 else 'Inactive'
            }
        
        return status

def test_ccxt_websocket():
    """测试CCXT WebSocket功能"""
    print("="*80)
    print("                    CCXT Pro WebSocket 测试")
    print("="*80)
    
    # 创建WebSocket管理器
    ws_manager = CCXTWebSocketManager()
    
    # 设置数据回调
    def on_data_update(exchange, symbol, bid, ask):
        print(f"📈 {exchange.upper()} {symbol}: Bid={bid:.6f}, Ask={ask:.6f}")
    
    ws_manager.set_data_callback(on_data_update)
    
    # 测试配置 - 使用CCXT标准符号格式
    test_symbols = {
        'binance': ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'],
        'okx': ['BTC/USDT:USDT', 'ETH/USDT:USDT'],  # OKX期货格式
        'bybit': ['BTC/USDT:USDT', 'ETH/USDT:USDT'], # Bybit期货格式
        'bitget': ['BTC/USDT:USDT', 'ETH/USDT:USDT'], # Bitget期货格式
    }
    
    try:
        # 启动WebSocket连接
        future = ws_manager.start_websocket_streams(test_symbols)
        
        print(f"\n开始监控60秒...")
        start_time = time.time()
        
        while time.time() - start_time < 60:
            time.sleep(10)
            
            # 显示连接状态
            status = ws_manager.get_connection_status()
            print(f"\n--- 连接状态 ({int(time.time() - start_time)}s) ---")
            
            for exchange, info in status.items():
                print(f"{exchange.upper()}: {info['active_symbols']}/{info['total_symbols']} active")
        
        # 显示最终统计
        print(f"\n最终统计:")
        price_data = ws_manager.get_price_data()
        for exchange, symbols_data in price_data.items():
            print(f"  {exchange.upper()}: {len(symbols_data)} 个交易对有数据")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    finally:
        # 停止所有连接
        ws_manager.stop_websocket_streams()
        print("测试完成")

if __name__ == "__main__":
    test_ccxt_websocket()
