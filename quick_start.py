#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 一键开始WebSocket监控
"""

import sys
import time
import logging
from 期货交易所交易对分析工具 import FuturesAnalyzer

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_monitor():
    """快速监控模式"""
    print("="*80)
    print("                    期货交易所实时价格监控")
    print("                        快速启动模式")
    print("="*80)
    print()
    
    # 配置参数
    exchanges = ['binance', 'okx']  # 主要交易所
    max_symbols = 30  # 每个交易所监控30个交易对
    monitor_time = 300  # 监控5分钟
    
    print(f"监控配置:")
    print(f"  交易所: {', '.join(exchanges)}")
    print(f"  每个交易所交易对数: {max_symbols}")
    print(f"  监控时间: {monitor_time}秒 ({monitor_time//60}分钟)")
    print()
    
    # 创建分析器
    analyzer = FuturesAnalyzer()
    
    try:
        # 1. 获取交易对数据
        print("📊 正在获取交易对数据...")
        analyzer.fetch_all_data(exchanges)
        print("✅ 交易对数据获取完成")
        print()
        
        # 2. 启动WebSocket连接
        print("🔌 正在启动WebSocket连接...")
        analyzer.start_websocket_streams(exchanges, max_symbols)
        print("✅ WebSocket连接已建立")
        print()
        
        # 3. 开始监控
        print("🚀 开始实时监控...")
        print("按 Ctrl+C 可随时停止监控")
        print("-" * 80)
        
        monitor_start = time.time()
        last_summary = 0
        last_arbitrage = 0
        
        while time.time() - monitor_start < monitor_time:
            current_time = time.time()
            elapsed = current_time - monitor_start
            remaining = monitor_time - elapsed
            
            # 每15秒显示价格摘要
            if current_time - last_summary >= 15:
                print(f"\n⏰ 监控进度: {elapsed:.0f}s / {monitor_time}s (剩余 {remaining:.0f}s)")
                analyzer.print_price_summary()
                last_summary = current_time
            
            # 每45秒检查套利机会
            if current_time - last_arbitrage >= 45:
                print(f"\n💰 套利机会检查 - {time.strftime('%H:%M:%S')}")
                analyzer.print_arbitrage_opportunities(min_spread_pct=0.05, max_results=5)
                last_arbitrage = current_time
            
            time.sleep(3)
        
        print(f"\n⏰ 监控时间结束")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断监控")
    except Exception as e:
        logger.error(f"监控过程中出现错误: {e}")
    finally:
        # 4. 停止WebSocket并显示最终统计
        print("\n🔌 正在停止WebSocket连接...")
        analyzer.stop_websocket_streams()
        
        print("\n" + "="*80)
        print("                        最终统计报告")
        print("="*80)
        
        # 显示最终价格摘要
        analyzer.print_price_summary()
        
        # 显示最终套利机会
        print("\n💰 最终套利机会:")
        analyzer.print_arbitrage_opportunities(min_spread_pct=0.02, max_results=10)
        
        print("\n✅ 监控完成！")

def interactive_mode():
    """交互模式"""
    print("="*80)
    print("                    交互式配置模式")
    print("="*80)
    
    # 选择交易所
    print("\n可用交易所:")
    all_exchanges = ['binance', 'okx', 'bybit', 'bitget']
    for i, ex in enumerate(all_exchanges, 1):
        print(f"  {i}. {ex}")
    
    try:
        choice = input("\n请选择交易所 (输入数字，用空格分隔，如: 1 2): ").strip()
        if not choice:
            exchanges = ['binance', 'okx']  # 默认
        else:
            indices = [int(x) - 1 for x in choice.split()]
            exchanges = [all_exchanges[i] for i in indices if 0 <= i < len(all_exchanges)]
    except:
        exchanges = ['binance', 'okx']  # 默认
    
    # 选择监控参数
    try:
        max_symbols = int(input(f"\n每个交易所监控多少个交易对? (默认30): ") or "30")
        monitor_time = int(input(f"监控多长时间(秒)? (默认300): ") or "300")
        min_spread = float(input(f"最小套利价差百分比? (默认0.05): ") or "0.05")
    except:
        max_symbols = 30
        monitor_time = 300
        min_spread = 0.05
    
    print(f"\n配置确认:")
    print(f"  交易所: {', '.join(exchanges)}")
    print(f"  每个交易所交易对数: {max_symbols}")
    print(f"  监控时间: {monitor_time}秒")
    print(f"  最小价差: {min_spread}%")
    
    input("\n按回车键开始监控...")
    
    # 开始监控
    analyzer = FuturesAnalyzer()
    
    try:
        analyzer.fetch_all_data(exchanges)
        analyzer.start_websocket_streams(exchanges, max_symbols)
        
        monitor_start = time.time()
        last_check = 0
        
        while time.time() - monitor_start < monitor_time:
            current_time = time.time()
            
            if current_time - last_check >= 20:
                analyzer.print_price_summary()
                analyzer.print_arbitrage_opportunities(min_spread, 5)
                last_check = current_time
            
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\n用户中断监控")
    finally:
        analyzer.stop_websocket_streams()
        print("\n监控结束")

def main():
    """主函数"""
    print("期货交易所WebSocket实时监控 - 快速启动")
    print()
    print("选择运行模式:")
    print("1. 快速监控 (推荐新手)")
    print("2. 交互配置")
    print("3. 退出")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            quick_monitor()
        elif choice == "2":
            interactive_mode()
        elif choice == "3":
            print("再见！")
        else:
            print("无效选择，使用快速监控模式")
            quick_monitor()
            
    except KeyboardInterrupt:
        print("\n\n再见！")
    except Exception as e:
        logger.error(f"程序出现错误: {e}")

if __name__ == "__main__":
    main()
