#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货交易所WebSocket实时数据监控使用示例
"""

import time
import logging
from 期货交易所交易对分析工具 import FuturesAnalyzer

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def example_basic_monitoring():
    """基础实时监控示例"""
    print("="*80)
    print("                    基础实时价格监控示例")
    print("="*80)
    
    # 创建分析器
    analyzer = FuturesAnalyzer()
    
    try:
        # 1. 获取交易对数据
        print("正在获取交易对数据...")
        analyzer.fetch_all_data(['binance', 'okx'])
        
        # 2. 启动WebSocket连接
        print("正在启动WebSocket连接...")
        analyzer.start_websocket_streams(['binance', 'okx'], max_symbols_per_exchange=20)
        
        # 3. 监控2分钟
        print("开始监控价格数据（2分钟）...")
        monitor_start = time.time()
        
        while time.time() - monitor_start < 120:  # 2分钟
            # 显示价格摘要
            analyzer.print_price_summary()
            
            # 等待10秒
            time.sleep(10)
        
        # 4. 停止WebSocket
        analyzer.stop_websocket_streams()
        
        print("✅ 基础监控示例完成！")
        
    except KeyboardInterrupt:
        print("用户中断监控")
        analyzer.stop_websocket_streams()
    except Exception as e:
        logger.error(f"监控失败: {e}")

def example_arbitrage_hunting():
    """套利机会寻找示例"""
    print("\n" + "="*80)
    print("                    套利机会寻找示例")
    print("="*80)
    
    # 创建分析器
    analyzer = FuturesAnalyzer()
    
    try:
        # 1. 获取所有交易所数据
        print("正在获取所有交易所数据...")
        analyzer.fetch_all_data(['binance', 'okx', 'bybit', 'bitget'])
        
        # 2. 启动WebSocket连接
        print("正在启动WebSocket连接...")
        analyzer.start_websocket_streams(['binance', 'okx', 'bybit'], max_symbols_per_exchange=30)
        
        # 3. 持续寻找套利机会
        print("开始寻找套利机会（3分钟）...")
        monitor_start = time.time()
        last_arbitrage_check = 0
        
        while time.time() - monitor_start < 180:  # 3分钟
            current_time = time.time()
            
            # 每30秒检查套利机会
            if current_time - last_arbitrage_check >= 30:
                print(f"\n--- 套利机会检查 {time.strftime('%H:%M:%S')} ---")
                analyzer.print_arbitrage_opportunities(min_spread_pct=0.05, max_results=10)
                last_arbitrage_check = current_time
            
            time.sleep(5)
        
        # 4. 停止WebSocket
        analyzer.stop_websocket_streams()
        
        print("✅ 套利寻找示例完成！")
        
    except KeyboardInterrupt:
        print("用户中断套利寻找")
        analyzer.stop_websocket_streams()
    except Exception as e:
        logger.error(f"套利寻找失败: {e}")

def example_price_tracking():
    """特定交易对价格跟踪示例"""
    print("\n" + "="*80)
    print("                    特定交易对价格跟踪示例")
    print("="*80)
    
    # 要跟踪的交易对
    target_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT']
    
    # 创建分析器
    analyzer = FuturesAnalyzer()
    
    try:
        # 1. 获取交易对数据
        print("正在获取交易对数据...")
        analyzer.fetch_all_data(['binance', 'okx'])
        
        # 2. 启动WebSocket连接
        print("正在启动WebSocket连接...")
        analyzer.start_websocket_streams(['binance', 'okx'], max_symbols_per_exchange=50)
        
        # 3. 跟踪特定交易对
        print(f"开始跟踪特定交易对: {', '.join(target_symbols)}")
        monitor_start = time.time()
        
        while time.time() - monitor_start < 120:  # 2分钟
            print(f"\n--- 价格更新 {time.strftime('%H:%M:%S')} ---")
            
            for exchange in ['binance', 'okx']:
                prices = analyzer.get_current_prices(exchange)
                print(f"\n{analyzer.exchanges[exchange]['name']}:")
                
                for symbol in target_symbols:
                    if symbol in prices:
                        price_info = prices[symbol]
                        spread = price_info['ask'] - price_info['bid']
                        spread_pct = (spread / price_info['bid']) * 100
                        
                        print(f"  {symbol:<10}: Bid={price_info['bid']:<10.6f} "
                              f"Ask={price_info['ask']:<10.6f} Spread={spread_pct:.3f}%")
                    else:
                        print(f"  {symbol:<10}: No data")
            
            time.sleep(15)  # 每15秒更新一次
        
        # 4. 停止WebSocket
        analyzer.stop_websocket_streams()
        
        print("✅ 价格跟踪示例完成！")
        
    except KeyboardInterrupt:
        print("用户中断价格跟踪")
        analyzer.stop_websocket_streams()
    except Exception as e:
        logger.error(f"价格跟踪失败: {e}")

def main():
    """主函数"""
    print("期货交易所WebSocket实时数据监控使用示例\n")
    
    # 示例1：基础监控
    example_basic_monitoring()
    
    # 示例2：套利机会寻找
    example_arbitrage_hunting()
    
    # 示例3：特定交易对跟踪
    example_price_tracking()
    
    print("\n" + "="*80)
    print("                    所有示例完成！")
    print("="*80)

if __name__ == "__main__":
    main()
