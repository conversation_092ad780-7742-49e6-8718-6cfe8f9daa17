#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("="*60)
    print("        期货交易所WebSocket工具依赖安装")
    print("="*60)
    
    # 必需的包列表
    required_packages = [
        "requests",
        "pandas", 
        "matplotlib",
        "seaborn",
        "numpy",
        "websockets"
    ]
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    print()
    
    # 检查已安装的包
    print("检查已安装的包...")
    installed = []
    missing = []
    
    for package in required_packages:
        # 特殊处理某些包名
        import_name = package
        if package == "matplotlib":
            import_name = "matplotlib.pyplot"
        
        if check_package(import_name.split('.')[0]):
            print(f"✅ {package} 已安装")
            installed.append(package)
        else:
            print(f"❌ {package} 未安装")
            missing.append(package)
    
    print()
    
    if not missing:
        print("🎉 所有依赖都已安装！")
        return
    
    # 安装缺失的包
    print(f"需要安装 {len(missing)} 个包...")
    print()
    
    failed = []
    for package in missing:
        print(f"正在安装 {package}...")
        if not install_package(package):
            failed.append(package)
    
    print()
    print("="*60)
    
    if not failed:
        print("🎉 所有依赖安装完成！")
        print()
        print("现在可以运行以下命令测试功能：")
        print("python test_websocket.py")
        print("python example_usage.py")
    else:
        print(f"❌ {len(failed)} 个包安装失败:")
        for package in failed:
            print(f"  - {package}")
        print()
        print("请手动安装失败的包：")
        for package in failed:
            print(f"pip install {package}")

if __name__ == "__main__":
    main()
