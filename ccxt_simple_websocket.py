#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的CCXT Pro WebSocket实现
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleCCXTWebSocket:
    """简化的CCXT Pro WebSocket管理器"""
    
    def __init__(self):
        self.price_data = {}
        self.running = False
        
    async def test_single_exchange(self, exchange_name: str, symbols: List[str]):
        """测试单个交易所的WebSocket连接"""
        try:
            import ccxt.pro as ccxtpro
            
            # 创建交易所实例
            exchange_class = getattr(ccxtpro, exchange_name)
            exchange = exchange_class({
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            logger.info(f"✅ {exchange_name.upper()} CCXT Pro instance created")
            
            # 测试不同的WebSocket方法
            methods_to_test = ['watch_order_book', 'watch_ticker', 'watch_trades']
            
            for method_name in methods_to_test:
                if hasattr(exchange, method_name):
                    logger.info(f"  📡 {exchange_name} supports {method_name}")
                    
                    try:
                        method = getattr(exchange, method_name)
                        
                        # 测试第一个符号
                        if symbols:
                            symbol = symbols[0]
                            logger.info(f"  🔍 Testing {method_name} for {symbol}")
                            
                            if method_name == 'watch_order_book':
                                result = await asyncio.wait_for(method(symbol, limit=5), timeout=10.0)
                                if result and 'bids' in result and 'asks' in result:
                                    bids = result['bids']
                                    asks = result['asks']
                                    if bids and asks:
                                        logger.info(f"  ✅ {symbol} OrderBook: Bid={bids[0][0]}, Ask={asks[0][0]}")
                                        
                                        # 存储数据
                                        if exchange_name not in self.price_data:
                                            self.price_data[exchange_name] = {}
                                        self.price_data[exchange_name][symbol] = {
                                            'bid': float(bids[0][0]),
                                            'ask': float(asks[0][0]),
                                            'timestamp': time.time()
                                        }
                                        
                            elif method_name == 'watch_ticker':
                                result = await asyncio.wait_for(method(symbol), timeout=10.0)
                                if result and 'bid' in result and 'ask' in result:
                                    bid = result['bid']
                                    ask = result['ask']
                                    if bid and ask:
                                        logger.info(f"  ✅ {symbol} Ticker: Bid={bid}, Ask={ask}")
                                        
                                        # 存储数据
                                        if exchange_name not in self.price_data:
                                            self.price_data[exchange_name] = {}
                                        self.price_data[exchange_name][symbol] = {
                                            'bid': float(bid),
                                            'ask': float(ask),
                                            'timestamp': time.time()
                                        }
                                        
                            elif method_name == 'watch_trades':
                                result = await asyncio.wait_for(method(symbol), timeout=10.0)
                                if result and len(result) > 0:
                                    latest_trade = result[-1]
                                    logger.info(f"  ✅ {symbol} Latest Trade: Price={latest_trade.get('price', 'N/A')}")
                                    
                    except asyncio.TimeoutError:
                        logger.warning(f"  ⏰ {method_name} timeout for {exchange_name}")
                    except Exception as e:
                        logger.warning(f"  ❌ {method_name} failed for {exchange_name}: {e}")
                else:
                    logger.info(f"  ❌ {exchange_name} does not support {method_name}")
            
            # 关闭连接
            await exchange.close()
            logger.info(f"✅ {exchange_name} connection closed")
            return True
            
        except ImportError:
            logger.error("❌ CCXT Pro not installed")
            return False
        except Exception as e:
            logger.error(f"❌ {exchange_name} test failed: {e}")
            return False

async def test_all_exchanges():
    """测试所有交易所"""
    print("="*80)
    print("                    CCXT Pro 简化WebSocket测试")
    print("="*80)
    
    ws_manager = SimpleCCXTWebSocket()
    
    # 测试配置
    exchanges_symbols = {
        'binance': ['BTC/USDT', 'ETH/USDT'],
        'okx': ['BTC/USDT:USDT', 'ETH/USDT:USDT'],
        'bybit': ['BTC/USDT:USDT', 'ETH/USDT:USDT'],
        'bitget': ['BTC/USDT:USDT', 'ETH/USDT:USDT'],
    }
    
    results = {}
    
    for exchange_name, symbols in exchanges_symbols.items():
        print(f"\n{'='*60}")
        print(f"测试 {exchange_name.upper()}")
        print(f"{'='*60}")
        
        try:
            result = await ws_manager.test_single_exchange(exchange_name, symbols)
            results[exchange_name] = result
        except Exception as e:
            logger.error(f"{exchange_name} test error: {e}")
            results[exchange_name] = False
    
    # 显示最终结果
    print(f"\n{'='*80}")
    print("                        测试结果汇总")
    print(f"{'='*80}")
    
    success_count = 0
    for exchange, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{exchange.upper():<10}: {status}")
        if success:
            success_count += 1
    
    print(f"\n成功连接: {success_count}/{len(results)} 个交易所")
    
    # 显示接收到的数据
    if ws_manager.price_data:
        print(f"\n接收到的价格数据:")
        for exchange, symbols_data in ws_manager.price_data.items():
            print(f"  {exchange.upper()}:")
            for symbol, price_info in symbols_data.items():
                print(f"    {symbol}: Bid={price_info['bid']:.6f}, Ask={price_info['ask']:.6f}")
    else:
        print(f"\n⚠️  未接收到价格数据")

async def test_continuous_monitoring():
    """测试持续监控"""
    print("="*80)
    print("                    CCXT Pro 持续监控测试")
    print("="*80)
    
    try:
        import ccxt.pro as ccxtpro
        
        # 选择一个可能工作的交易所进行持续监控
        exchange = ccxtpro.okx({
            'sandbox': False,
            'enableRateLimit': True,
        })
        
        symbol = 'BTC/USDT:USDT'
        logger.info(f"开始持续监控 OKX {symbol}")
        
        # 持续监控30秒
        start_time = time.time()
        update_count = 0
        
        while time.time() - start_time < 30:
            try:
                # 尝试获取orderbook
                orderbook = await asyncio.wait_for(exchange.watch_order_book(symbol, limit=1), timeout=5.0)
                
                if orderbook and 'bids' in orderbook and 'asks' in orderbook:
                    bids = orderbook['bids']
                    asks = orderbook['asks']
                    
                    if bids and asks:
                        bid = float(bids[0][0])
                        ask = float(asks[0][0])
                        spread = ask - bid
                        spread_pct = (spread / bid) * 100
                        
                        update_count += 1
                        elapsed = int(time.time() - start_time)
                        
                        print(f"[{elapsed:2d}s] {symbol}: Bid={bid:>10.2f}, Ask={ask:>10.2f}, "
                              f"Spread={spread:>6.2f} ({spread_pct:>5.3f}%) - Update #{update_count}")
                
            except asyncio.TimeoutError:
                logger.warning("监控超时，继续尝试...")
            except Exception as e:
                logger.error(f"监控错误: {e}")
                break
        
        await exchange.close()
        logger.info(f"持续监控完成，共接收 {update_count} 次更新")
        
    except ImportError:
        logger.error("❌ CCXT Pro not installed")
    except Exception as e:
        logger.error(f"持续监控失败: {e}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'continuous':
        # 持续监控模式
        asyncio.run(test_continuous_monitoring())
    else:
        # 基础测试模式
        asyncio.run(test_all_exchanges())

if __name__ == "__main__":
    main()
