# 期货交易所交易对分析工具 - WebSocket实时数据版

这是一个支持实时WebSocket数据流的期货交易所交易对分析工具，支持币安(Binance)、OKX、Bybit、Bitget四大交易所的实时价格监控和套利机会发现。

## 新增功能

### 🚀 WebSocket实时数据流
- 实时获取买一价、卖一价数据
- 支持多交易所同时连接
- 自动重连和错误处理
- 内存中持续更新价格数据

### 📊 套利机会发现
- 实时计算跨交易所价差
- 自动识别套利机会
- 可配置最小价差阈值
- 按收益率排序显示

### 📈 实时监控面板
- 价格数据摘要显示
- 连接状态监控
- 活跃交易对统计
- 最新更新时间跟踪

## 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖：
- `websockets`: WebSocket客户端
- `requests`: HTTP请求
- `pandas`: 数据处理
- `matplotlib`: 数据可视化
- `asyncio`: 异步编程

## 使用方法

### 1. 基础分析（原有功能）

```bash
# 分析所有交易所
python 期货交易所交易对分析工具.py

# 分析特定交易所
python 期货交易所交易对分析工具.py --exchanges binance okx

# 导出数据
python 期货交易所交易对分析工具.py --export data.json
```

### 2. 实时WebSocket监控

```bash
# 启用WebSocket实时监控（默认60秒）
python 期货交易所交易对分析工具.py --websocket

# 指定监控时间和交易所
python 期货交易所交易对分析工具.py --websocket --monitor-time 300 --exchanges binance okx

# 限制每个交易所的交易对数量
python 期货交易所交易对分析工具.py --websocket --max-symbols 30
```

### 3. 套利机会监控

```bash
# 启用套利监控
python 期货交易所交易对分析工具.py --websocket --arbitrage

# 设置最小价差阈值
python 期货交易所交易对分析工具.py --websocket --arbitrage --min-spread 0.2

# 完整套利监控
python 期货交易所交易对分析工具.py --websocket --arbitrage --monitor-time 600 --min-spread 0.1 --max-symbols 50
```

## 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--exchanges` | 指定交易所 | 所有交易所 |
| `--websocket, -w` | 启用WebSocket | False |
| `--max-symbols` | 每个交易所最大交易对数 | 50 |
| `--arbitrage, -a` | 显示套利机会 | False |
| `--min-spread` | 最小价差百分比 | 0.1 |
| `--monitor-time` | 监控时间（秒） | 60 |
| `--output, -o` | 报告输出文件 | None |
| `--export, -e` | 数据导出文件 | None |
| `--no-plot` | 跳过可视化 | False |

## 代码示例

### 基础WebSocket使用

```python
from 期货交易所交易对分析工具 import FuturesAnalyzer

# 创建分析器
analyzer = FuturesAnalyzer()

# 获取交易对数据
analyzer.fetch_all_data(['binance', 'okx'])

# 启动WebSocket连接
analyzer.start_websocket_streams(['binance', 'okx'], max_symbols_per_exchange=20)

# 获取实时价格
prices = analyzer.get_current_prices('binance')
print(f"币安价格数据: {len(prices)} 个交易对")

# 停止WebSocket
analyzer.stop_websocket_streams()
```

### 套利机会检测

```python
# 寻找套利机会
opportunities = analyzer.get_arbitrage_opportunities(min_spread_pct=0.1)

for opp in opportunities[:5]:
    print(f"{opp['symbol']}: {opp['spread_pct']:.2f}% "
          f"({opp['buy_exchange']} -> {opp['sell_exchange']})")
```

## 支持的交易所

| 交易所 | WebSocket支持 | 订阅类型 |
|--------|---------------|----------|
| Binance | ✅ | bookTicker |
| OKX | ✅ | books5 |
| Bybit | ✅ | orderbook.1 |
| Bitget | ✅ | books5 |

## 数据格式

### 价格数据结构
```python
{
    'exchange_name': {
        'SYMBOL': {
            'bid': 价格,      # 买一价
            'ask': 价格,      # 卖一价
            'timestamp': 时间戳  # 更新时间
        }
    }
}
```

### 套利机会结构
```python
{
    'symbol': '交易对',
    'buy_exchange': '买入交易所',
    'sell_exchange': '卖出交易所',
    'buy_price': 买入价格,
    'sell_price': 卖出价格,
    'spread': 价差,
    'spread_pct': 价差百分比,
    'timestamp': 时间戳
}
```

## 测试

运行测试脚本：
```bash
python test_websocket.py
```

运行使用示例：
```bash
python example_usage.py
```

## 注意事项

1. **网络连接**: 确保网络连接稳定，WebSocket需要持续连接
2. **API限制**: 各交易所有连接数限制，建议合理设置max_symbols参数
3. **数据延迟**: 实际套利需考虑网络延迟和交易执行时间
4. **风险提示**: 本工具仅用于数据分析，实际交易请谨慎评估风险

## 更新日志

### v2.0.0
- ✅ 新增WebSocket实时数据流支持
- ✅ 新增套利机会自动发现
- ✅ 新增实时监控面板
- ✅ 支持多交易所并发连接
- ✅ 新增价格数据内存存储
- ✅ 新增连接状态监控

### v1.0.0
- ✅ 基础交易对分析功能
- ✅ 数据可视化
- ✅ 报告生成
- ✅ 数据导出

## 许可证

MIT License
